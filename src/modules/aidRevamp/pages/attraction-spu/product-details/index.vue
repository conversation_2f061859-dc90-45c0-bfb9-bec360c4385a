<template>
  <div class="container">
    <Content :activity_id="actGlobal.activity_id" :package_id="package_id">
      <ObtSectionContainer ref="obtSection" show-skeleton />
    </Content>
  </div>
</template>

<script>
import ObtSectionContainer from '~src/modules/aidRevamp/components/common/obt-section/index.js'
import ActPageStoreMixin from '~src/modules/aidRevamp/mixins/act-page-store.js'
import Content from './content.vue'

export default {
  name: 'ProductDetails',
  components: {
    ObtSectionContainer,
    Content
  },
  mixins: [ActPageStoreMixin],
  methods: {
    async save(force = false) {
      const obtSectionRef = this.$refs.obtSection

      if (!obtSectionRef) {
        return
      }

      return await obtSectionRef.validateAndGetData({ force })
    }
  }
}
</script>
