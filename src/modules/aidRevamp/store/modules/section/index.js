import Vue from 'vue'
import {
  SET_SECTIONS,
  RESET_SECTION_DATA,
  INIT_PROXY_FORM_DATA,
  UPDATE_FORM_DATA,
  UPDATE_MERCHANT_DATA,
  UPDATE_SELECTION_DATA
} from './mutation-types.js'
import { computeSections } from '~src/modules/aidRevamp/components/common/obt-section/utils'
import { isAttraction } from '~src/modules/aidRevamp/utils/index.js'
const defaultCurrUpdateField = {
  key: undefined,
  value: undefined,
  oldValue: undefined
}

const state = {
  sections: [],
  form: {},
  selectionData: {},
  currUpdateField: defaultCurrUpdateField,
  // merchant-select 与 merchant-self-confirm 联动
  currMerchantData: {},
  obtLoading: false
}

export default {
  namespaced: true,
  state,
  getters: {
    sectionsGetter(state) {
      return state.sections
    },
    formDataGetter(state) {
      return state.form
    },
    currUpdateFieldGetter(state) {
      return state.currUpdateField
    },
    currMerchantDataGetter(state) {
      return state.currMerchantData
    },
    curFormatSections(state) {
      return state.sections
    },
    curFormatSectionsLoading(state) {
      return state.obtLoading
    },
    currFormatSelectionData(state) {
      return state.selectionData
    }
  },
  mutations: {
    [SET_SECTIONS](state, data) {
      state.sections = data
    },
    [RESET_SECTION_DATA](state) {
      Vue.set(state, 'currMerchantData', {})
      Vue.set(state, 'currUpdateField', Object.assign({}, defaultCurrUpdateField))
    },
    [INIT_PROXY_FORM_DATA](state, { key, value }) {
      Vue.set(state.form, key, value)
    },
    [UPDATE_FORM_DATA](state, { key, value, oldValue }) {
      Vue.set(state.form, key, value)
      Vue.set(state, 'currUpdateField', { key, value, oldValue })
    },
    [UPDATE_MERCHANT_DATA](state, data) {
      Vue.set(state, 'currMerchantData', data)
    },
    [UPDATE_SELECTION_DATA](state, data) {
      Vue.set(state, 'selectionData', data)
    }
  },
  actions: {
    async getSectionDataByMenu({ state, rootState }, data = {}) {
      const isAttr = isAttraction(data.sub_category_id)
      state.obtLoading = true
      let result = await ajax.post(ADMIN_API.aidRevamp.getProductData, {
        data
      })

      if (result) {
        state.selectionData = result
        state.sections = computeSections(result, { isAttr, rootState })
      }
      state.obtLoading = false
      return state.selectionData
    }
  }
}
