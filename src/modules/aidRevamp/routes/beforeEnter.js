import { isMerchant } from '@/env'

import store from '~src/modules/aidRevamp/store/index.js'
import Vuex from 'vuex'
import Vue from 'vue'
Vue.use(Vuex)
const storeInstance = new Vuex.Store(store)

import {
  SET_IS_TOURS,
  SET_IS_ATTRACTION
} from '~src/modules/aidRevamp/store/modules/common/mutation-types.js'
import { PAGE_LEVEL_DICT } from '~src/modules/aidRevamp/utils/const.js'
import { SET_LOCK_STATUS } from '~src/modules/aidRevamp/store/modules/global/mutation-types.js'
import { checkLock } from '~src/modules/aidRevamp/utils/navigation.js'

function getToQueryString(to) {
  // eslint-disable-next-line no-unused-vars
  const { package_id, ...other } = to.query || {}

  return new URLSearchParams(other).toString()
}

function getToActivityList() {
  if (isMerchant) {
    location.href = `${location.origin}/mspa/experiencesadmincommon/act/management`
  } else {
    location.href = `${location.origin}/act/activity/list`
  }
}

export function createBeforeEnter() {
  return async function (to, from, next) {
    const activity_id = to.params.id
    const pid = to.query.package_id ? Number(to.query.package_id) : undefined

    if (activity_id) {
      if (!isMerchant) {
        // 非商户端需要检查状态，以实现仅阅读功能
        const lock = await checkLock(activity_id, { package_id: pid })
        if (lock !== 0) {
          getToActivityList()
          return
        }
      }

      const data = await storeInstance.dispatch('getActCategory2action', {
        activity_id,
        page_from: to.query.page_from,
        refresh: true
      })

      if (!data) {
        getToActivityList()
        return
      }

      let routeParamsModified = false
      if (isMerchant && to.query.lang !== data.source_language) {
        // Merchant 端需要检查语言
        to.query.lang = data.source_language
        routeParamsModified = true
      }

      const query = getToQueryString(to)
      const url = `${location.origin}/mspa/experiencesadmincommon/act/activity/basic/${activity_id}?${query}`
      if (!data?.page_type?.includes(PAGE_LEVEL_DICT.spu)) {
        // 非spu活动
        location.href = url
        return
      } else {
        // spu活动
        const spuIds = data?.spu_id_list || []
        if (pid || !spuIds?.length) {
          // pid存在 或者 spuIds为空
          if (!spuIds?.includes(pid)) {
            // pid存在，则判断spu_id_list不包含pid
            location.href = url
            return
          }
        } else {
          // pid不存在，则拼接spu_id_list第一个元素
          to.query.package_id = spuIds[0]
          routeParamsModified = true
        }
      }

      // 如果路由参数被修改，重新导航
      if (routeParamsModified) {
        next(to)
        return
      }

      let lockStatus = await ajax.get({
        url: ADMIN_API.act.edit_protect,
        params: {
          activity_id,
          package_id: pid,
          page_from: 'spu'
        }
      })

      if (lockStatus === false) {
        location.href = `${location.origin}/act/activity/list`
        return
      }

      storeInstance.commit(`global/${SET_LOCK_STATUS}`, lockStatus)
    }

    storeInstance.commit(`common/${SET_IS_TOURS}`, true)
    storeInstance.commit(`common/${SET_IS_ATTRACTION}`, false)
    next()
  }
}

export function createBeforeAttractionEnter(config = {}) {
  const { redirectUrl = '', pageTypeKey = '', needValidate = true } = config

  return async function (to, from, next) {
    const activity_id = to.params.id
    const pid = to.query.package_id ? Number(to.query.package_id) : undefined

    // 如果没有 activity_id，报错并跳转到指定的列表页
    if (!activity_id) {
      location.href = `${location.origin}${redirectUrl}`
      return
    }

    try {
      if (!isMerchant) {
        const lock = await checkLock(activity_id, { package_id: pid })
        if (lock !== 0) {
          location.href = `${location.origin}${redirectUrl}`
          return
        }
      }

      const data = await storeInstance.dispatch('getActCategory2action', {
        activity_id,
        page_from: to.query.page_from,
        refresh: true
      })

      if (!data) {
        getToActivityList()
        return
      }

      const isLocalhost = window.location.host.includes('localhost')
      const baseURL = isLocalhost
        ? `${window.location.origin}`
        : `${window.location.origin}/mspa/experiencesadmincommon`

      // 3.  Attraction 判断 page_type
      const pageType = data?.page_type || []
      console.log(pageType, pageTypeKey,'pageTypepageTypepageType');
      if (!pageType.includes(pageTypeKey) && pageTypeKey === PAGE_LEVEL_DICT.att) {
        // 如果没有指定的 page_type，跳转到指定的列表页
        location.href = `${baseURL}${redirectUrl}`
        return
      }

      debugger;
      // 4. 如果是SPU页面，判断 package_id 是否合法
      if (pageTypeKey === PAGE_LEVEL_DICT.spu) {
        const spuIds = data?.spu_id_list || []
        if (!pid || !spuIds.includes(+pid)) {
          // 如果 pid 不存在，或 spu_id_list 不包含当前 pid，跳转到指定的列表页
          location.href = `${baseURL}${redirectUrl}`
          return
        }
      }

      // 1. 检查编辑状态
      const editStatus = await ajax.get({
        url: ADMIN_API.act.edit_protect,
        params: {
          activity_id,
          package_id: pid,
          page_from: 'spu'
        }
      })

      if (editStatus === false) {
        // 有人正在编辑，跳转到指定的列表页
        location.href = `${location.origin}${redirectUrl}`
        return
      }

      if (!needValidate) {
        next()
        return
      }

      storeInstance.commit(`global/${SET_LOCK_STATUS}`, editStatus)
      storeInstance.commit(`common/${SET_IS_TOURS}`, false)
      storeInstance.commit(`common/${SET_IS_ATTRACTION}`, true)
      // 允许进入页面
      next()
    } catch (error) {
      console.error('Error in beforeEnter:', error)
      // 如果请求失败，默认跳转到指定的列表页
      location.href = `${location.origin}${redirectUrl}`
    }
  }
}
