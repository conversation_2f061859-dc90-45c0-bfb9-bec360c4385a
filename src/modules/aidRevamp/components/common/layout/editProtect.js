import ReleaseLock from '@activity/utils/release-lock'
import { PAGE_LEVEL_DICT } from '~src/modules/aidRevamp/utils/const.js'

const EDIT_PROTECT_INTERVAL = 120 * 1000

export const LOCK_SPU_PAGE_FROM = 'spu'

const mixins = {
  computed: {
    activityId() {
      return this.aid
    },
    packageId() {
      const pid = this.$route.query.package_id
      return pid ? Number(pid) : undefined
    }
  },
  watch: {
    activityId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.action()
        }
      }
    }
  },

  beforeDestroy() {
    this.releaseLockInstance?.destroy?.()
  },
  methods: {
    action() {
      this.startEditProtect()
      this.$nextTick(() => {
        this.releaseLockInstance = new ReleaseLock({
          activityId: this.activityId,
          onVisible: () => {
            this.dispatchEditProtect()
            this.startEditProtect()
          },
          onRelease: this.stopEditProtect
        })
      })
    },
    async dispatchEditProtect() {
      if (this.activityId) {
        let lockStatus = await ajax.get({
          url: ADMIN_API.act.edit_protect,
          params: {
            activity_id: this.activityId,
            package_id: this.packageId,
            page_type: LOCK_SPU_PAGE_FROM
          }
        })

        // lockStatus 可能是对象
        const need_redirect = lockStatus && typeof lockStatus === 'object' && lockStatus.need_redirect
        if (need_redirect) {
          this.$message.error(this.$t('193016', { user_name: lockStatus.kick_manager }))
        }

        if (lockStatus === false || need_redirect) {
          this.redirectToBasic()
        }
      }
    },
    startEditProtect(interval = EDIT_PROTECT_INTERVAL) {
      clearInterval(this.apiEditTimer)
      this.apiEditTimer = setInterval(this.dispatchEditProtect, interval)
    },
    stopEditProtect() {
      clearInterval(this.apiEditTimer)
    },
    redirectToBasic() {
      if (this.isTours) {
        location.href = `${location.origin}/act/activity/list`
        return
      } else {
        const baseURL =
          process.env.NODE_ENV === 'production'
            ? `${window.location.origin}/mspa/experiencesadmincommon`
            : `${window.location.origin}`

        if (this.pageLevel === PAGE_LEVEL_DICT.att) {
          // attraction list
          location.href = `${baseURL}/aid/attraction-list`
        } else {
          // spu list
          location.href = `${baseURL}/aid/spu-list`
        }
      }
    }
  }
}

export default mixins
