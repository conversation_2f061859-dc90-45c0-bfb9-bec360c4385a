<template>
  <div class="common-field-container">
    <h2 v-if="title" :id="keyFlag" class="common-field-title" :class="{ 'flex-center': hasTicketTypeField }">
      {{ title }}
      <!-- ticket type 对应的 group 的折叠需要特殊处理，待优化 -->
      <!-- <a-icon
        v-if="hasTicketTypeField"
        :type="validityChunkIsUnfold ? 'down' : 'right'"
        @click="validityChunkIsUnfold = !validityChunkIsUnfold"
      /> -->
    </h2>

    <a-form-model ref="form" class="common-form-style" :model="form" label-width="200px">
      <div
        v-for="orderKey in orderList"
        :key="orderKey"
        class="form-item js-dataset-field"
        :data-is-required="getRequiredByKey(orderKey)"
        :data-is-suggest-filling="getSuggestFillingByKey(orderKey)"
        :data-field="orderKey"
      >
        <!-- spa 预约 -->
        <template v-if="displayFactory('reservation_required', orderKey)">
          <a-form-model-item
            :label="$t('121700')"
            prop="reservation_required"
            :rules="getRulesFactory('reservation_required')"
            :colon="false"
          >
            <a-radio-group v-model="form.reservation_required" :disabled="getDisabled(orderKey)">
              <a-radio :value="1">{{ $t('121701') }}</a-radio>
              <a-radio :value="2">{{ $t('121702') }}</a-radio>
            </a-radio-group>
            <StrongTips :tips="getCurrentStrongTips('reservation_required')" />
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('reservation_method', orderKey)">
          <a-form-model-item
            v-if="hideReservationFileds"
            :label="$t('121703')"
            prop="reservation_method"
            :rules="getRulesFactory('reservation_method')"
            :colon="false"
            :class="{ 'reset-label': !showReservationMethodOptions }"
          >
            <a-radio-group
              v-show="showReservationMethodOptions"
              v-model="form.reservation_method"
              :disabled="getDisabled(orderKey)"
            >
              <a-radio :value="1">{{ $t('121704') }}</a-radio>
              <a-radio :value="2">{{ $t('121705') }}</a-radio>
            </a-radio-group>
            <StrongTips :tips="getCurrentStrongTips('reservation_method')" />
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('reservation_others_cut_off_time', orderKey)">
          <a-form-model-item
            v-if="hideReservationFileds && form.reservation_method === 2"
            prop="reservation_others_cut_off_time"
            :rules="getRulesFactory('reservation_others_cut_off_time')"
            :colon="false"
            class="special-form-item"
          >
            <span slot="label" class="form-item-label">{{ $t('168874') }}</span>
            <div>
              <a-radio-group
                v-model="form.reservation_others_cut_off_time.type"
                direction="vertical"
                @change="handleChangeReservationCutTimeType"
              >
                <a-radio :value="1"> {{ $t('174481') }} </a-radio>
                <a-radio :value="2" class="flex-content">
                  <div class="reservation-other-cut-off-time">
                    {{ reservationOthersCutOffTimeText[0] }}
                    <NumberUnitSelector
                      v-model="form.reservation_others_cut_off_time.time_config"
                      :disabled="form.reservation_others_cut_off_time.type === 1"
                      class="num-unit-comp"
                    />
                    {{ reservationOthersCutOffTimeText[1] }}
                  </div>
                </a-radio>
              </a-radio-group>
            </div>
            <StrongTips :tips="getCurrentStrongTips('reservation_others_cut_off_time')" />
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('reservation_other_content', orderKey)">
          <a-form-model-item
            v-if="hideReservationFileds && form.reservation_method === 2"
            prop="reservation_other_content"
            :rules="getRulesFactory('reservation_other_content')"
            :colon="false"
            class="hide-label"
          >
            <div
              v-for="(contact, index) in form.reservation_other_content.contact"
              :key="index"
              class="reservation-input-group-wrap"
            >
              <div class="reservation-input-group">
                <a-select
                  v-model="contact.type"
                  :disabled="getDisabled(orderKey)"
                  :placeholder="$t('global_select')"
                  style="width: 150px; margin-right: 10px"
                >
                  <a-select-option v-for="item in reservationContactType" :key="item.key" :value="item.key">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
                <a-input v-model="contact.content" :disabled="getDisabled(orderKey)" style="width: 280px" />
                <a-icon
                  v-if="form.reservation_other_content.contact.length > 1 && !getDisabled(orderKey)"
                  type="delete"
                  class="reservation-del"
                  @click="handleReservationContactDel(index)"
                />
              </div>
              <div v-if="checkHttp(contact)" class="error-message">{{ $t('49651') }}</div>
            </div>
            <a-button
              :disabled="getDisabled(orderKey)"
              class="btn-merchantPkgContact-add form-width reservation-add"
              @click="handleReservationContactAdd"
            >
              {{ $t('global_add') }}
            </a-button>
            <a-textarea
              v-model="form.reservation_other_content.note"
              :disabled="getDisabled(orderKey) || isPublishWithAi"
              :placeholder="$t('92242')"
              :auto-size="{ minRows: 4, maxRows: 4 }"
              class="reservation-note"
            />
            <StrongTips :tips="getCurrentStrongTips('reservation_other_content')" />
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('reservation_no_show_policy', orderKey)">
          <a-form-model-item
            v-if="hideReservationMethodAfterFileds"
            prop="reservation_no_show_policy"
            :rules="getRulesFactory('reservation_no_show_policy')"
            :colon="false"
          >
            <span slot="label">
              <span>{{ $t('121709') }}</span>

              <a-tooltip placement="right" overlay-class-name="common-tooltip-style">
                <template slot="title">
                  <span>{{ $t('121710') }}</span>
                </template>
                <a-icon style="margin-left: 8px" theme="filled" type="info-circle" />
              </a-tooltip>
            </span>
            <a-radio-group
              v-model="form.reservation_no_show_policy"
              :disabled="getDisabled(orderKey) || form.cancellation_policy === 0"
            >
              <a-radio :value="1">{{ $t('121711') }}</a-radio>
              <a-radio :value="2">{{ $t('121712') }}</a-radio>
            </a-radio-group>
            <StrongTips :tips="getCurrentStrongTips('reservation_no_show_policy')" />
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('reservation_amendment_policy', orderKey)">
          <a-form-model-item
            v-if="hideReservationMethodAfterFileds && form.reservation_no_show_policy === 1"
            :label="$t('121713')"
            prop="reservation_amendment_policy"
            :rules="getRulesFactory('reservation_amendment_policy')"
            :colon="false"
          >
            <div class="num-unit-wrap">
              <span>{{ reservationAmendmentPolicyText[0] }}</span>
              <div class="num-unit-text-left"></div>
              <NumberUnitSelector
                v-model="form.reservation_amendment_policy"
                :disabled="getDisabled(orderKey)"
                class="num-unit-comp"
              />
              <div class="num-unit-text-right"></div>
              <span>{{ reservationAmendmentPolicyText[1] }}</span>
            </div>
            <StrongTips :tips="getCurrentStrongTips('reservation_amendment_policy')" />
          </a-form-model-item>
        </template>

        <template v-else-if="displayGroupFactory('available_amendment_group', orderKey)">
          <a-form-model-item
            v-if="hideReservationMethodAfterFileds"
            :label="$t('121715')"
            :colon="false"
            :required="getGroupRequired(orderKey)"
            style="min-height: unset"
          ></a-form-model-item>
        </template>
        <template v-else-if="displayFactory('reservation_week_timeslot', orderKey)">
          <a-form-model-item
            v-if="hideReservationMethodAfterFileds"
            prop="reservation_week_timeslot"
            :rules="getRulesFactory('reservation_week_timeslot')"
            :colon="false"
            class="special-form-item"
          >
            <span slot="label" class="form-item-label">{{ $t('121716') }}</span>
            <div
              v-for="(week, index) in form.reservation_week_timeslot"
              :key="index"
              class="week-timeslot-wrap"
            >
              <WeekTimeslot
                :data="week"
                :other-week-data="getOtherTimeslotWeek(index)"
                :disabled="getDisabled(orderKey)"
                @change="(value) => handleReservationWeekTimeslotChange(value, index)"
              />
              <a-icon
                v-if="form.reservation_week_timeslot.length > 1 && !getDisabled(orderKey)"
                type="delete"
                class="timeslot-del"
                @click="handleTimeslotDelete(index)"
              />
            </div>

            <div
              v-if="!getDisabled(orderKey)"
              class="reservation-date-add-btn"
              :class="{ disabled: checkAddDisabled }"
              @click="handleReservationWeekTimeslotAdd"
            >
              <a-icon class="reservation-date-add-btn-icon" type="plus-circle" />
              <div>{{ $t('global_add') }}</div>
            </div>
            <StrongTips :tips="getCurrentStrongTips('reservation_week_timeslot')" />
          </a-form-model-item>
        </template>
        <template v-else-if="displayFactory('reservation_block_out_date', orderKey)">
          <a-form-model-item
            v-if="hideReservationMethodAfterFileds"
            prop="reservation_block_out_date"
            :rules="getRulesFactory('reservation_block_out_date')"
            :colon="false"
            class="special-form-item"
          >
            <span slot="label" class="form-item-label">{{ $t('121720') }}</span>
            <div
              v-for="(item, index) in form.reservation_block_out_date"
              :key="index"
              class="reservation-range-picker-wrap"
            >
              <a-range-picker
                :value="item"
                value-format="YYYY/MM/DD"
                class="reservation-range-picker v-disabled-exclude"
                :disabled="getDisabled(orderKey)"
                @change="(value) => handleReservationBlockOutDateChange(value, index)"
              >
                <a-icon slot="suffixIcon" type="calendar" />
              </a-range-picker>
              <a-icon
                v-if="form.reservation_block_out_date.length > 1 && !getDisabled(orderKey)"
                type="delete"
                class="block-del"
                @click="handleDeleteReservationBlockOutDate(index)"
              />
            </div>
            <div
              v-if="!getDisabled(orderKey)"
              class="reservation-date-add-btn"
              @click="handleAddReservationBlockOutDate"
            >
              <a-icon class="reservation-date-add-btn-icon" type="plus-circle" />
              <div>{{ $t('global_add') }}</div>
            </div>
            <StrongTips :tips="getCurrentStrongTips('reservation_block_out_date')" />
          </a-form-model-item>
        </template>
        <template v-else-if="displayFactory('reservation_cut_off_time', orderKey)">
          <a-form-model-item
            v-if="hideReservationMethodAfterFileds"
            prop="reservation_cut_off_time"
            :rules="getRulesFactory('reservation_cut_off_time')"
            :colon="false"
            class="special-form-item"
          >
            <span slot="label" class="form-item-label">{{ $t('121723') }}</span>
            <div class="num-unit-wrap">
              <span>{{ reservatioCutOffTimeText[0] }}</span>
              <div class="num-unit-text-left"></div>
              <NumberUnitSelector
                v-model="form.reservation_cut_off_time"
                :disabled="getDisabled(orderKey)"
                class="num-unit-comp"
              />
              <div class="num-unit-text-right"></div>
              <span>{{ reservatioCutOffTimeText[1] }}</span>
            </div>
            <StrongTips :tips="getCurrentStrongTips('reservation_cut_off_time')" />
          </a-form-model-item>
        </template>
        <!-- spa 预约 end -->

        <!-- * Product type -->
        <template v-else-if="displayFactory('product_type', orderKey)">
          <a-form-model-item
            :label="$t('73093')"
            prop="product_type"
            :rules="getRulesFactory('product_type')"
            :colon="false"
          >
            <a-radio-group v-model="form.product_type" :disabled="getDisabled(orderKey)">
              <a-radio :value="0">{{ $t('73094') }}</a-radio>
              <!-- <a-radio :value="1" :disabled="isFnd">{{ $t('73095') }}</a-radio> -->
              <a-radio :value="1">{{ $t('73095') }}</a-radio>
              <QuestionIcon
                :message="
                  getBlankLink(
                    $t('74487'),
                    'https://docs.google.com/presentation/d/1RGxAyJ1j4zbrMEC7xPd3C3emJ0P2TC4v7SBuZBmWETk/edit#slide=id.g123a3885ad9_0_691'
                  )
                "
                :overlay-style="{ maxWidth: '400px' }"
                component="a-popover"
              />
            </a-radio-group>
            <StrongTips :tips="getCurrentStrongTips('product_type')" />
          </a-form-model-item>
        </template>

        <!-- * Standalone PKGs -->
        <template v-else-if="displayFactory('combo_standalone_pkgs', orderKey)">
          <a-form-model-item
            prop="combo_standalone_pkgs"
            :rules="getRulesFactory('combo_standalone_pkgs')"
            :colon="false"
          >
            <span slot="label">
              <span>{{ $t('73097') }}</span>
              <QuestionIcon
                :message="$t('73082')"
                :overlay-style="{ maxWidth: '400px' }"
                component="a-popover"
              />
            </span>
            <a-alert type="warning" style="margin-bottom: 10px">
              <div slot="message" v-html="$t('74529')"></div>
            </a-alert>
            <StandalonePkgs :list="form.combo_standalone_pkgs || []" :disabled="getDisabled(orderKey)" />
            <StrongTips :tips="getCurrentStrongTips('combo_standalone_pkgs')" />
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('pkg_name', orderKey)">
          <tpl_special_multi_language_input
            ref="pkg_name"
            v-model="form.pkg_name"
            class="exclude-disabled"
            v-bind="
              getMultiAttrFactory('pkg_name', {
                label: $t('package_info_name')
              })
            "
            langField="language"
            valField="name"
            :initFchema="{ desc: '' }"
            :requiredEn="true"
            :disabled="getDisabled(orderKey) || isPublishWithAi"
            :showSuffixCount="true"
            :config="getConfig(schemaConfig['pkg_name'])"
            data-field="pkg_name"
          />
        </template>

        <template v-else-if="displayFactory('package_image', orderKey)">
          <a-form-model-item
            :label="$t('114386')"
            prop="package_image"
            :rules="getRulesFactory('package_image')"
            :colon="false"
          >
            <image-upload
              v-model="form.package_image"
              :cloudinary-options="packageImageCloudinaryOptions"
              :disabled="getDisabled(orderKey)"
              @validateField="validateField('package_image')"
            ></image-upload>
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('package_tips', orderKey)">
          <a-form-model-item
            :label="$t('act_auto_filled_original')"
            prop="package_tips"
            :rules="getRulesFactory('package_tips')"
            :colon="false"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('package_tips')"
              :line-clamp="3"
            />
            <ShimAntdTooltip
              placement="right"
              overlayClassName="common-tooltip-style"
              :title="getCurrentDescription('package_tips')"
            >
              <a-input
                :disabled="getDisabled(orderKey) || isPublishWithAi"
                v-model="form.package_tips"
                type="textarea"
                :autoSize="{ minRows: 3, maxRows: 6 }"
                :placeholder="$t('global_please_input')"
              ></a-input>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('package_type', orderKey)">
          <a-form-model-item
            :label="$t('package_type')"
            prop="package_type"
            :rules="getRulesFactory('package_type')"
            :colon="false"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('package_type')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('package_type')"
              :rich-tips="getCurrentRichTips('package_type')"
            >
              <a-select
                v-model="form.package_type"
                v-check-val="
                  () => {
                    return Object.values(fnb_package_types).includes(form.package_type)
                  }
                "
                :disabled="getDisabled(orderKey)"
                popper-class="popper-class"
                :placeholder="$t('global_please_select')"
              >
                <a-select-option v-for="(value, key) in fnb_package_types" :key="value" :value="+value">{{
                  $t(key)
                }}</a-select-option>
              </a-select>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('pkg_subname', orderKey)">
          <tpl_special_multi_language_input
            ref="pkg_subname"
            type="textarea"
            :title="getCurrentDescription('pkg_subname')"
            :maxlength="50"
            v-model="form.pkg_subname"
            v-bind="
              getMultiAttrFactory('pkg_subname', {
                label: $t('package_info_desc'),
                placeholder: $t('package_info_description_placeholder'),
                hideLangTip: true
              })
            "
            langField="language"
            valField="name"
            :disabled="getDisabled(orderKey) || isPublishWithAi"
            :showSuffixCount="true"
            :config="getConfig(schemaConfig['pkg_subname'])"
          />
        </template>

        <template v-else-if="displayFactory('pkg_unique_selling_point', orderKey)">
          <div>
            <span>{{ $t('125198') }}</span>
            <a-icon
              v-tooltip="{
                visible: true,
                placement: 'right',
                content: `
                    <p>
                      ${$t('125199')}
                    </p>
                  `
              }"
              style="margin-left: 8px"
              theme="filled"
              type="info-circle"
            />
          </div>

          <FieldBindingGroup
            ref="pkg_unique_selling_point"
            field="pkg_unique_selling_point"
            :all-variables="all_variables"
            :schema-config="schemaConfig"
            v-bind="getFieldAttrGroupDataByField(orderKey)"
            :render-component-on-ref-tag="renderComponentOnRefTag"
            :order-list="orderList"
            :form="form"
            :show-summary="false"
            :readonly="schemaConfig['pkg_unique_selling_point'].access_permission === 1"
            :ref-form="refForm"
            :is-publish-with-ai="isPublishWithAi"
            :title="getCurrentDescription('pkg_unique_selling_point')"
            :rich-tips="getCurrentRichTips('pkg_unique_selling_point')"
            @fieldAttributeGroupVN="$listeners.fieldAttributeGroupVN"
            @changeData="onChangeData"
            @changeGroupSummary="changeGroupSummary"
          >
            <template #title>
              {{ $t('42688') }}
            </template>
          </FieldBindingGroup>
        </template>

        <template v-else-if="displayFactory('price_displaying', orderKey)">
          <a-form-model-item
            prop="price_displaying"
            :rules="getRulesFactory('price_displaying')"
            :colon="false"
            style="min-height: auto"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('price_displaying')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('price_displaying')"
              :rich-tips="getCurrentRichTips('price_displaying')"
            >
              <a-checkbox
                :disabled="getDisabled(orderKey)"
                style="margin: 16px 0 0 0"
                :defaultChecked="!!form.price_displaying"
                @change="(e) => (form.price_displaying = e.target.checked ? 1 : 0)"
              >
                {{ $t('act_skip_displaying_pkg_price') }}
              </a-checkbox>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('pkg_merchant', orderKey)">
          <MerchantSelect
            ref="merchantSelect"
            v-model="form.pkg_merchant"
            :disabled="getDisabled(orderKey)"
            :activity-id="activity_id"
            :package-info="pkg"
            :rules="getRulesFactory('pkg_merchant')"
            :title="getCurrentDescription('pkg_merchant')"
            :description="getCurrentStrongTips('pkg_merchant')"
            @change="changeMerchant"
          ></MerchantSelect>
        </template>

        <template v-else-if="displayFactory('merchant_confirm', orderKey)">
          <a-form-model-item
            :label="$t('merchant_self_confirm')"
            prop="merchant_confirm"
            :rules="getRulesFactory('merchant_confirm')"
            :colon="false"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('merchant_confirm')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription2tplPackageMerchant('merchant_confirm')"
              :rich-tips="getCurrentRichTips('merchant_confirm')"
            >
              <a-radio-group
                v-model="form.merchant_confirm"
                v-check-val="
                  () => {
                    return [0, 1].includes(form.merchant_confirm)
                  }
                "
                :disabled="calcMerchantDisabled(orderKey)"
              >
                <a-radio :value="1">Yes</a-radio>
                <a-radio :value="0">No</a-radio>
              </a-radio-group>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>
        <template v-else-if="displayFactory('auto_suspend_sale', orderKey)">
          <a-form-model-item
            v-if="disableVoucherLevel"
            prop="auto_suspend_sale"
            :colon="false"
            :rules="getRulesFactory('auto_suspend_sale')"
          >
            <span slot="label">
              <span>{{ $t('119180') }}</span>
              <a-icon
                v-tooltip="{
                  visible: true,
                  placement: 'right',
                  content: `
                    <p>
                      ${$t('119181')}
                    </p>
                  `
                }"
                style="margin-left: 8px"
                theme="filled"
                type="info-circle"
              />
            </span>
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('auto_suspend_sale')"
              :line-clamp="3"
            />
            <a-switch
              v-tooltip="{
                placement: 'right',
                overlayClassName: 'common-tooltip-style',
                visible: !!getCurrentDescription('auto_suspend_sale'),
                content: getCurrentDescription('auto_suspend_sale')
              }"
              :checked="!!form.auto_suspend_sale"
              :disabled="getDisabled(orderKey)"
              @change="changeAutoSuspend"
            />
          </a-form-model-item>
        </template>
        <template v-else-if="displayFactory('pkg_contact', orderKey)">
          <a-form-model-item prop="pkg_contact" :rules="getRulesFactory('pkg_contact')" :colon="false">
            <span slot="label">
              <span>{{ $t('merchant_package_contact') }}</span>
              <a-tooltip placement="right" overlay-class-name="common-tooltip-style">
                <template slot="title">
                  <p>
                    {{ $t('108762') }}
                    <img
                      style="width: 100%; margin-top: 12px"
                      src="https://res.klook.com/image/upload/w_400/contact_info_eg_bw6chh.png"
                    />
                  </p>
                </template>
                <a-icon style="margin-left: 8px" theme="filled" type="info-circle" />
              </a-tooltip>
            </span>
            <span v-for="(mpc, index) in form.pkg_contact" :key="index">
              <DescMarkdownContent
                v-if="index === 0"
                class="strong-tips"
                placement="right"
                :is-inline="false"
                :desc="getCurrentStrongTips('pkg_contact')"
                :line-clamp="3"
              />

              <div :style="{ marginTop: index !== 0 ? '15px' : '' }">
                <a-input-group compact>
                  <a-select
                    v-model="mpc.media"
                    :disabled="getDisabled(orderKey)"
                    style="width: 150px; margin-right: 10px"
                    @change="onContactChange(mpc)"
                  >
                    <a-select-option
                      v-for="contactType in merchantPkgContactType"
                      :key="contactType.key"
                      :value="contactType.key"
                    >
                      {{ contactType.text }}
                    </a-select-option>
                  </a-select>
                  <shim-antd-tooltip
                    overlay-class-name="common-tooltip-style"
                    placement="right"
                    style="display: inline-block"
                    :title="getCurrentDescription('pkg_contact')"
                  >
                    <a-input
                      v-if="mpc.media == 'customized'"
                      v-model="mpc.title"
                      :disabled="getDisabled(orderKey)"
                      :placeholder="$t('global_please_input')"
                      :style="getContactStyle(mpc)"
                      style="margin-right: 10px"
                    />
                    <a-input
                      v-model="mpc.number"
                      :disabled="getDisabled(orderKey)"
                      :placeholder="$t('global_please_input')"
                      :style="getContactStyle(mpc)"
                      @change="onContactChange(mpc)"
                    />
                    <i
                      v-if="index !== 0 && !getDisabled(orderKey)"
                      class="common-delete-btn"
                      style="margin-top: -3px"
                      @click="handleDelMerchantPkgContact(mpc)"
                    >
                      <svg-icon icon-name="trash" />
                    </i>
                  </shim-antd-tooltip>
                </a-input-group>
              </div>
            </span>
            <a-button
              :disabled="getDisabled(orderKey)"
              class="btn-merchantPkgContact-add form-width"
              @click="handleAddMerchantPkgContact"
            >
              {{ $t('global_add') }}
            </a-button>
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('ticket_type', orderKey)">
          <a-form-model-item prop="ticket_type" :rules="getRulesFactory('ticket_type')" :colon="false">
            <template #label>
              <span style="display: inline-flex; align-items: center">
                {{ $t('ticket_type') }}
                <a-icon
                  :type="validityChunkIsUnfold ? 'down' : 'right'"
                  style="vertical-align: baseline"
                  @click="validityChunkIsUnfold = !validityChunkIsUnfold"
                />
              </span>
            </template>
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('ticket_type')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('ticket_type')"
              :rich-tips="getCurrentRichTips('ticket_type')"
            >
              <a-select
                v-model="form.ticket_type"
                v-check-val="
                  () => {
                    return [...Object.values(fnb_ticket_types), 3].includes(form.ticket_type)
                  }
                "
                :disabled="getDisabled(orderKey)"
                :placeholder="$t('global_please_select')"
              >
                <a-select-option
                  v-for="(key, name) in ticketTypeOpts"
                  :key="key"
                  :value="+key"
                  :disabled="getTicketTypeDisabled(+key)"
                >
                  {{ $t(name) }}
                  &nbsp;
                  <a-popover
                    v-if="name === 'fnb_open_ticket'"
                    placement="topLeft"
                    :title="$t('fnb_open_ticket_with_title')"
                    :overlay-style="{ width: '500px', zIndex: 2000 }"
                  >
                    <div
                      slot="content"
                      class="pre-cntent"
                      v-html="getBlankLink($t('fnb_open_ticket_with_content'), docLink)"
                    ></div>
                    <a-icon type="info-circle" theme="filled" />
                  </a-popover>
                </a-select-option>
                <a-select-option
                  v-if="!isMerchant && !hideOpenTicketWithoutCalendar"
                  key="open_ticket"
                  :value="3"
                  :label="$t('fnb_open_ticket_without_title')"
                  :disabled="getOpenDateTicketTypeDisabled()"
                >
                  {{ $t('fnb_open_ticket_without_title') }}
                  &nbsp;
                  <a-popover
                    placement="topLeft"
                    :title="$t('fnb_open_ticket_without_title')"
                    :overlayStyle="{ width: '500px', zIndex: 2000 }"
                  >
                    <pre
                      slot="content"
                      class="pre-cntent"
                      v-html="getBlankLink($t('fnb_open_ticket_without_content'), docLink)"
                    ></pre>
                    <a-icon type="info-circle" theme="filled" />
                  </a-popover>
                </a-select-option>
              </a-select>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <!-- start 有效期联动 -->
        <template v-else-if="displayFactory('validity_model', orderKey)">
          <a-form-model-item
            v-show="displayValidityModel"
            prop="validity_model"
            :rules="getRulesFactory('validity_model')"
            :colon="false"
          >
            <div slot="label" class="form-item-custom-label">
              {{ $t('200772') }}
            </div>

            <div style="margin: -12px 0 12px; color: #888">{{ $t('42684') }}</div>

            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('validity_model')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('validity_model')"
              :rich-tips="getCurrentRichTips('validity_model')"
            >
              <a-radio-group v-model="form.validity_model" :disabled="getDisabled(orderKey)">
                <a-radio
                  v-for="opt in validityModelOpts"
                  :key="opt.value"
                  :value="opt.value"
                  :disabled="opt.disabled"
                >
                  {{ opt.label }}
                </a-radio>
              </a-radio-group>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>
        <!-- 激活有效期 -->
        <template v-else-if="displayFactory('activation_validity', orderKey)">
          <div v-show="form.validity_model === 1">
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('activation_validity')"
              :line-clamp="3"
            />

            <FieldBindingGroup
              ref="activation_validity"
              class="exclude-disabled"
              field="activation_validity"
              :all-variables="all_variables"
              :schema-config="schemaConfig"
              v-bind="getFieldAttrGroupDataByField(orderKey)"
              :order-list="orderList"
              :show-summary="false"
              :form="form"
              :readonly="getDisabled(orderKey)"
              :ref-form="refForm"
              :is-publish-with-ai="isPublishWithAi"
              :title="getCurrentDescription('activation_validity')"
              :rich-tips="getCurrentRichTips('activation_validity')"
              @unfoldAllGroups="validityChunkIsUnfold = true"
              @fieldAttributeGroupVN="$listeners.fieldAttributeGroupVN"
              @changeData="onChangeData"
              @changeGroupSummary="changeGroupSummary"
            >
              <template #title>
                {{ $t('42687') }}
              </template>
            </FieldBindingGroup>
          </div>
        </template>

        <template v-else-if="displayFactory('aid_voucher_type_desc', orderKey)">
          <div class="aid_voucher_type_desc-wrapper">
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('aid_voucher_type_desc')"
              :line-clamp="3"
            />

            <FieldBindingGroup
              ref="aid_voucher_type_desc"
              class="exclude-disabled"
              field="aid_voucher_type_desc"
              :all-variables="all_variables"
              :schema-config="schemaConfig"
              v-bind="getFieldAttrGroupDataByField(orderKey)"
              :render-component-on-ref-tag="renderComponentOnRefTag"
              :order-list="orderList"
              :form="form"
              :show-summary="false"
              :readonly="schemaConfig['aid_voucher_type_desc'].access_permission === 1"
              :ref-form="refForm"
              :is-publish-with-ai="isPublishWithAi"
              :title="getCurrentDescription('aid_voucher_type_desc')"
              :rich-tips="getCurrentRichTips('aid_voucher_type_desc')"
              @unfoldAllGroups="validityChunkIsUnfold = true"
              @fieldAttributeGroupVN="$listeners.fieldAttributeGroupVN"
              @changeData="onChangeData"
              @changeGroupSummary="changeGroupSummary"
            >
              <template #title>
                {{ $t('42688') }}
              </template>
            </FieldBindingGroup>
          </div>
        </template>

        <template v-else-if="displayFactory('usage_validity', orderKey)">
          <div v-if="form.ticket_type !== undefined">
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('usage_validity')"
              :line-clamp="3"
            />

            <FieldBindingGroup
              ref="usage_validity"
              :key="form.ticket_type"
              class="exclude-disabled"
              field="usage_validity"
              :all-variables="all_variables"
              :schema-config="schemaConfig"
              v-bind="getFieldAttrGroupDataByField(orderKey)"
              :render-component-on-ref-tag="renderComponentOnRefTag"
              :order-list="orderList"
              :form="form"
              :show-summary="false"
              :readonly="schemaConfig['usage_validity'].access_permission === 1"
              :default-required-gourp-items="usageValidityFieldRequired ? ['title_valid_time'] : []"
              :ref-form="refForm"
              :is-publish-with-ai="isPublishWithAi"
              :title="getCurrentDescription('usage_validity')"
              :rich-tips="getCurrentRichTips('usage_validity')"
              @unfoldAllGroups="validityChunkIsUnfold = true"
              @fieldAttributeGroupVN="$listeners.fieldAttributeGroupVN"
              @changeData="onChangeData"
              @changeGroupSummary="changeGroupSummary"
            >
              <template #title>
                {{ $t('42688') }}
              </template>
            </FieldBindingGroup>
          </div>
        </template>

        <template v-else-if="displayFactory('participation_validity', orderKey)">
          <div v-show="form.validity_model === 2">
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('participation_validity')"
              :line-clamp="3"
            />

            <FieldBindingGroup
              ref="participation_validity"
              field="participation_validity"
              class="exclude-disabled"
              :all-variables="all_variables"
              :schema-config="schemaConfig"
              v-bind="getFieldAttrGroupDataByField(orderKey)"
              :render-component-on-ref-tag="renderComponentOnRefTag"
              :order-list="orderList"
              :form="form"
              :show-summary="false"
              :readonly="schemaConfig['participation_validity'].access_permission === 1"
              :ref-form="refForm"
              :is-publish-with-ai="isPublishWithAi"
              :title="getCurrentDescription('participation_validity')"
              :rich-tips="getCurrentRichTips('participation_validity')"
              @unfoldAllGroups="validityChunkIsUnfold = true"
              @fieldAttributeGroupVN="$listeners.fieldAttributeGroupVN"
              @changeData="onChangeData"
              @changeGroupSummary="changeGroupSummary"
            >
              <template #customTitle>
                {{ $t('78345') }}
              </template>
            </FieldBindingGroup>
          </div>
        </template>

        <!-- field group -->
        <div
          v-else-if="orderKey === 'field_group_summary' && showFieldAttrGroupSummary"
          class="field-attr-summaries"
        >
          <template v-for="curr in allGroupsSummary">
            <template v-for="group in curr.groupsSummary">
              <groupSummary
                v-if="group.summary && group.summary.length"
                class="summary-group-list"
                :group="group"
                :key="`${curr.field}-${group.id}`"
              >
                <h2 slot="groupName">
                  {{ fieldAttrGroupNameDict[curr.field] || '' }} {{ $t('act_group_summary') }}
                </h2>
              </groupSummary>
            </template>
          </template>
        </div>
        <!-- end 有效期联动 -->

        <!-- 出库类型 -->
        <template v-else-if="displayFactory('inventory_type', orderKey)">
          <!-- <div :class="[$store.state.isMC2BD ? 'dsp-none' : '']"> -->
          <a-form-model-item
            prop="inventory_type"
            :rules="getRulesFactory('inventory_type')"
            :colon="false"
            :label="isMerchant ? $t('172672') : $t('stock_out_type')"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('inventory_type')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              class="inventory-type-tooltip-wrapper"
              :title="getCurrentDescription('inventory_type')"
              :rich-tips="getCurrentRichTips('inventory_type')"
            >
              <a-radio-group
                v-model="form.inventory_type"
                v-check-val="
                  () => {
                    return stockOutOptions.some((item) => item.value === form.inventory_type)
                  }
                "
                :class="{ 'common-inline-flex-column': isMerchant }"
                :disabled="getDisabled(orderKey)"
                @change="restrictChange('inventory_type')"
              >
                <a-radio
                  v-for="opt in stockOutOptions"
                  :key="opt.value"
                  :value="opt.value"
                  :disabled="getInventoryTypeDisableStatus(opt.value)"
                >
                  <span class="common-inline-flex-column">
                    <span>
                      {{ opt.label }}
                      <a-tooltip v-if="opt.tips">
                        <template slot="title">
                          {{ opt.tips }}
                        </template>
                        <a-icon type="info-circle" theme="filled" />
                      </a-tooltip>
                    </span>
                    <span v-if="opt.desc" class="common-inline-flex-column__desc">{{ opt.desc }}</span>
                  </span>
                </a-radio>
              </a-radio-group>
            </ShimAntdTooltip>
          </a-form-model-item>
          <!-- </div> -->
        </template>

        <!-- 凭证类型 -->
        <template v-else-if="displayFactory('voucher_type', orderKey) && showVoucherType && e_voucher">
          <a-form-model-item
            prop="voucher_type"
            :rules="getRulesFactory('voucher_type')"
            :colon="false"
            :label="$t('merchant_confirm_type')"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('voucher_type')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('voucher_type')"
              :rich-tips="getCurrentRichTips('voucher_type')"
            >
              <a-select
                :disabled="getDisabled(orderKey)"
                v-model="form.voucher_type"
                :placeholder="$t('global_please_select')"
                @change="restrictChange"
                v-check-val="
                  () => {
                    return merchant_confirm_types.find((o) => o.id === form.voucher_type)
                  }
                "
              >
                <a-select-option v-for="{ id, name } in merchant_confirm_types" :key="id" :value="+id">{{
                  name
                }}</a-select-option>
              </a-select>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <!-- cancellation_policy || confirmation_time || voucher_get_method -->
        <template v-else-if="displayOptionBindingTitlesComp(orderKey)">
          <OptionBindingTitles
            v-if="!getOptionBindingTitlesCompConf(orderKey).hide"
            ref="optionBindingTitles"
            class="exclude-disabled"
            :data-field="orderKey"
            :field="orderKey"
            :rules="getRulesFactory(orderKey)"
            :colon="false"
            :all-variables="all_variables"
            :schema-config="schemaConfig"
            :order-list="orderList"
            :form="form"
            :isComboPKG="isComboPKG"
            :isPassProducts="isPassProducts"
            :option-group="optionGroup"
            :is-publish-with-ai="isPublishWithAi"
            :title="getCurrentDescription(orderKey)"
            :rich-tips="getCurrentRichTips(orderKey)"
            style="width: fit-content"
            v-bind="getOptionBindingTitlesCompConf(orderKey).attr || {}"
            v-on="getOptionBindingTitlesCompConf(orderKey).listeners || {}"
            @fieldAttributeGroupVN="$listeners.fieldAttributeGroupVN"
            @changeData="onChangeData"
          >
            <template #strongTips>
              <div v-if="getOptionBindingTitlesCompConf(orderKey).tips">
                {{ getOptionBindingTitlesCompConf(orderKey).tips }}
              </div>
              <DescMarkdownContent
                class="strong-tips"
                placement="right"
                :is-inline="false"
                :desc="getCurrentStrongTips(orderKey)"
                :line-clamp="3"
              />
            </template>
          </OptionBindingTitles>
        </template>

        <!-- 凭证使用方法 -->
        <template v-else-if="displayFactory('voucher_usage', orderKey)">
          <a-form-model-item
            v-if="e_voucher"
            prop="voucher_usage"
            :colon="false"
            :rules="getRulesFactory('voucher_usage')"
          >
            <span slot="label">
              <span>{{ $t('80496') }}</span>
              <a-tooltip placement="right" overlay-class-name="common-tooltip-style">
                <template slot="title">
                  <p>
                    {{ $t('80295') }}
                    <img
                      style="width: 100%; margin-top: 12px"
                      src="https://res.klook.com/image/upload/activities/oj2dh2iz6sy0mcblalas.jpg"
                    />
                  </p>
                </template>
                <a-icon style="margin-left: 8px" theme="filled" type="info-circle" />
              </a-tooltip>
            </span>

            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('voucher_usage')"
              :line-clamp="3"
            />

            <component
              :is="false && !!getCurrentDescription('voucher_usage') ? 'a-tooltip' : 'span'"
              a-tooltip
              placement="right"
              overlay-class-name="common-tooltip-style"
            >
              <template slot="title">
                <span>{{ getCurrentDescription('voucher_usage') }}</span>
              </template>
              <a-switch
                :checked="form.voucher_usage === voucher_usages[80333]"
                :disabled="getDisabled(orderKey)"
                @change="changeVoucherUsage"
              />
            </component>
          </a-form-model-item>
        </template>

        <template v-if="displayFactory('dynamic_voucher_code', orderKey)">
          <a-form-model-item
            v-if="e_voucher"
            :label="$t('204571-Voucher Display Mode')"
            prop="dynamic_voucher_code"
            :rules="getRulesFactory('dynamic_voucher_code')"
            :colon="false"
          >
            <a-radio-group v-model="form.dynamic_voucher_code" :options="dynamic_voucher_code_options">
            </a-radio-group>
          </a-form-model-item>

          <a-alert
            v-if="form.dynamic_voucher_code === dynamic_voucher_code_enum['dynamic']"
            type="warning"
            show-icon
            style="margin-top: 16px"
          >
            <template #message>
              <p>{{ $t('204574') }}</p>
              <p>{{ $t('204575') }}</p>
            </template>
          </a-alert>
        </template>

        <!-- 凭证兑换码级别 -->
        <template v-else-if="displayFactory('voucher_code_level', orderKey)">
          <a-form-model-item
            v-if="form.inventory_type !== StockOutValDict.API && e_voucher"
            prop="voucher_code_level"
            :rules="getRulesFactory('voucher_code_level')"
            :colon="false"
            :label="$t('ob_voucher_code_level')"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('voucher_code_level')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('voucher_code_level')"
              :rich-tips="getCurrentRichTips('voucher_code_level')"
            >
              <a-select
                v-model="form.voucher_code_level"
                v-check-val="
                  () => {
                    return voucher_levels.map((arr) => arr[0]).includes(form.voucher_code_level)
                  }
                "
                :disabled="disableVoucherLevel || getDisabled(orderKey)"
                :placeholder="$t('global_please_select')"
              >
                <a-select-option
                  v-for="[key, name, t] in voucher_levels"
                  :key="name"
                  :value="key"
                  :label="name"
                >
                  {{ name }}
                  &nbsp;
                  <a-popover
                    placement="topLeft"
                    :content="t"
                    :overlay-style="{ width: '200px', zIndex: 2000 }"
                  >
                    <a-icon type="info-circle" theme="filled" />
                  </a-popover>
                </a-select-option>
              </a-select>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <!-- 定时发布/取消 -->
        <AutoPubUnpub
          v-else-if="displayFactory('auto_pub_unpub', orderKey)"
          :data.sync="form"
          :rules="getRulesFactory('auto_pub_unpub')"
          :tips="getCurrentStrongTips('auto_pub_unpub')"
          :disabled="getDisabled(orderKey)"
          :lock-auto-publish="lockAutoPublish"
          :lock-auto-warm-up="lockAutoWarmUp"
          :description="getCurrentDescription('auto_pub_unpub')"
        />

        <!-- Special Set-Up start-->
        <template v-else-if="displayFactory('min_max_bookings', orderKey)">
          <a-form-model-item
            :colon="false"
            prop="min_max_bookings"
            :rules="getRulesFactory('min_max_bookings')"
            :label="$t('package_info_max_min_participants')"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('min_max_bookings')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('min_max_bookings')"
              :rich-tips="getCurrentRichTips('min_max_bookings')"
            >
              <tpl_package_min_max_booking
                ref="min_max_bookings"
                :disabled="getDisabled(orderKey)"
                :value="form.min_max_bookings"
                @changeData="onChangeData"
              />
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('calendar_day', orderKey)">
          <a-form-model-item
            prop="calendar_day"
            :rules="getRulesFactory('calendar_day')"
            :colon="false"
            :label="$t('74294')"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('calendar_day')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="$t('package_displayed_month_tips')"
              :rich-tips="getCurrentRichTips('calendar_day')"
              :overlay-style="{ maxWidth: '320px' }"
            >
              <a-select
                :disabled="getDisabled(orderKey) || disabledCalendarMonth"
                v-model="form.calendar_day"
                :placeholder="$t('global_please_select')"
                showSearch
                v-check-val="
                  () => {
                    return _extendDayOptions.find((o) => o.value === form.calendar_day)
                  }
                "
              >
                <a-select-option v-for="(item, index) in _extendDayOptions" :key="index" :value="item.value"
                  >{{ item.label }}
                </a-select-option>
              </a-select>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <!-- Calendar Extend(day or month) -->
        <template v-else-if="displayFactory('calendar_extend', orderKey)">
          <a-form-model-item
            style="margin-top: 16px"
            prop="calendar_extend"
            :rules="getRulesFactory('calendar_extend')"
            :colon="false"
          >
            <a-radio-group v-model="form.calendar_extend.type">
              <a-radio-button :value="0">Days</a-radio-button>
              <a-radio-button :value="1">Months</a-radio-button>
            </a-radio-group>

            <a-form-model-item
              v-if="form.calendar_extend.type === 0"
              style="margin-top: -10px"
              :rules="getRulesFactory('calendar_extend')"
              :label="$t('74294')"
            >
              <a-tooltip
                placement="right"
                overlayClassName="common-tooltip-style"
                :title="$t('package_displayed_month_tips')"
                :overlayStyle="{ maxWidth: '320px' }"
              >
                <a-select
                  :disabled="getDisabled(orderKey) || disabledCalendarMonth"
                  v-model="form.calendar_extend.days"
                  :placeholder="$t('global_please_select')"
                  showSearch
                  v-check-val="
                    () => {
                      return _extendDayOptions.find((o) => o.value === form.calendar_extend.days)
                    }
                  "
                >
                  <a-select-option v-for="(item, index) in _extendDayOptions" :key="index" :value="item.value"
                    >{{ item.label }}
                  </a-select-option>
                </a-select>
              </a-tooltip>
            </a-form-model-item>
            <a-form-model-item
              v-if="form.calendar_extend.type === 1"
              style="margin-top: -10px"
              :rules="getRulesFactory('calendar_extend')"
              :label="$t('83349')"
            >
              <DescMarkdownContent
                class="strong-tips"
                placement="right"
                :is-inline="false"
                :desc="getCurrentStrongTips('calendar_extend')"
                :line-clamp="3"
              />

              <a-tooltip
                placement="right"
                overlayClassName="common-tooltip-style"
                :title="$t('package_displayed_month_tips')"
                :overlayStyle="{ maxWidth: '320px' }"
              >
                <a-select
                  :disabled="getDisabled(orderKey) || disabledCalendarMonth"
                  v-model="form.calendar_extend.months"
                  :placeholder="$t('global_please_select')"
                  showSearch
                  v-check-val="
                    () => {
                      return _extendMonthOptions.find((o) => o.value === form.calendar_extend.months)
                    }
                  "
                >
                  <a-select-option
                    v-for="(item, index) in _extendMonthOptions"
                    :key="index"
                    :value="item.value"
                    >{{ item.label }}
                  </a-select-option>
                </a-select>
              </a-tooltip>
            </a-form-model-item>

            <StrongTips :tips="getCalculatedDateTips" />
          </a-form-model-item>
        </template>

        <template v-else-if="displayFactory('guarantee_group', orderKey)">
          <a-form-model-item
            v-show="form.ticket_type === 1"
            prop="guarantee_group"
            :rules="getRulesFactory('guarantee_group')"
            :colon="false"
          >
            <span slot="label">
              <span>{{ $t('81813') }}</span>
              <a-tooltip placement="right" overlay-class-name="common-tooltip-style">
                <template slot="title">
                  <p>
                    <img
                      style="width: 100%; margin-top: 12px"
                      src="https://res.klook.com/image/upload/guaranted_group_uuowy0.png"
                    />
                  </p>
                </template>
                <a-icon style="margin-left: 8px" theme="filled" type="info-circle" />
              </a-tooltip>
            </span>
            <a-popconfirm v-model="showminGroupVisible" placement="topRight" @cancel="handleGroupCancel">
              <div slot="title" style="width: 300px">
                {{ minGroupChangeTip }}
              </div>
            </a-popconfirm>
            <a-tooltip
              placement="right"
              overlayClassName="common-tooltip-style"
              :title="$t('81810')"
              :overlayStyle="{ maxWidth: '320px' }"
            >
              <a-select
                class="mini-select js-guarantee-group-min-group"
                v-model="form.guarantee_group.min_group"
                :placeholder="$t('81811')"
                :disabled="getDisabled(orderKey)"
                showSearch
                allowClear
                v-check-val="
                  () => {
                    return _groupDayOptions.find((o) => o.value === form.guarantee_group.min_group)
                  }
                "
              >
                <a-select-option v-for="(item, index) in _groupDayOptions" :key="index" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
              -
              <a-select
                class="mini-select"
                :disabled="getDisabled(orderKey)"
                v-model="form.guarantee_group.max_group"
                :placeholder="$t('81812')"
                allowClear
                showSearch
                v-check-val="
                  () => {
                    return _groupDayOptions.find((o) => o.value === form.guarantee_group.max_group)
                  }
                "
              >
                <a-select-option v-for="(item, index) in formatOptions" :key="index" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-tooltip>
          </a-form-model-item>
        </template>
        <!-- Special Set-Up end-->

        <!-- 发布至以下用户类型可见 -->
        <template v-else-if="displayFactory('show_account_type', orderKey)">
          <a-form-model-item
            prop="show_account_type"
            :rules="getRulesFactory('show_account_type')"
            :colon="false"
            :label="$t('act_if_bookable')"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('show_account_type')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('show_account_type')"
              :rich-tips="getCurrentRichTips('show_account_type')"
            >
              <a-radio-group
                v-model="form.show_account_type"
                v-check-val="
                  () => {
                    return hasOwnProperty.call(platforms, form.show_account_type)
                  }
                "
                :disabled="getDisabled(orderKey)"
              >
                <a-radio
                  v-for="(name, value) in platforms"
                  :key="+value"
                  :value="+value"
                  :disabled="showAccountTypeOptionsDisabled(+value)"
                >
                  {{ name }}
                </a-radio>
              </a-radio-group>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <!-- 可转让 -->
        <template v-else-if="displayFactory('transferable', orderKey)">
          <a-form-model-item
            prop="transferable"
            :rules="getRulesFactory('transferable')"
            :colon="false"
            :label="$t('act_if_transferable')"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('transferable')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('transferable')"
              :rich-tips="getCurrentRichTips('transferable')"
            >
              <a-radio-group
                :disabled="getDisabled(orderKey)"
                v-model="form.transferable"
                v-check-val="
                  () => {
                    return [0, 1].includes(form.transferable)
                  }
                "
              >
                <a-radio class="radio" :value="1"> {{ $t('act_transferable_yes') }}</a-radio>
                <a-radio class="radio" :value="0"> {{ $t('act_transferable_no') }}</a-radio>
              </a-radio-group>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <!-- 是否已提供台湾履约保证 -->
        <template v-else-if="displayFactory('escrow_guarantee', orderKey)">
          <a-form-model-item
            prop="escrow_guarantee"
            :rules="getRulesFactory('escrow_guarantee')"
            :colon="false"
            :label="$t('act_guarantee_for_package_1')"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('escrow_guarantee')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('escrow_guarantee')"
              :rich-tips="getCurrentRichTips('escrow_guarantee')"
            >
              <a-radio-group
                v-model="form.escrow_guarantee"
                v-check-val="
                  () => {
                    return [true, false].includes(form.escrow_guarantee)
                  }
                "
                :disabled="getDisabled(orderKey)"
              >
                <a-radio :value="true">
                  {{ $t('global_yes') }}
                </a-radio>
                <a-radio :value="false">
                  {{ $t('global_no') }}
                </a-radio>
              </a-radio-group>
            </ShimAntdTooltip>
          </a-form-model-item>
          <!-- 台湾履约保证证明 -->
          <a-form-model-item
            v-if="displayFactory('escrow_guarantee_photo', 'escrow_guarantee_photo')"
            v-show="form.escrow_guarantee"
            prop="escrow_guarantee_photo"
            :colon="false"
            :rules="getRulesFactory('escrow_guarantee_photo')"
          >
            <span slot="label">
              {{ $t('74248') }}
              <QuestionIcon
                :message="$t('74250')"
                :overlay-style="{ maxWidth: '400px' }"
                component="a-popover"
                placement="rightTop"
              >
                <img
                  v-for="(item, index) in iconImages"
                  :key="index"
                  class="question-icon-img"
                  :src="item.url"
                  @click="previewImage(item)"
                />
              </QuestionIcon>
            </span>

            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('escrow_guarantee_photo')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('escrow_guarantee_photo')"
              :rich-tips="getCurrentRichTips('escrow_guarantee_photo')"
            >
              <EscrowGuarantee
                ref="EscrowGuarantee"
                v-model="form.escrow_guarantee_photo"
                :photo-list="form.escrow_guarantee_photo"
                :disabled="getDisabled('escrow_guarantee_photo')"
              />
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <!-- 是否征税 -->
        <template v-else-if="displayFactory('does_it_include', orderKey)">
          <a-form-model-item
            prop="does_it_include"
            :rules="getRulesFactory('does_it_include')"
            :colon="false"
            :label="$t('act_if_meet_criterias')"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('does_it_include')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              :title="getCurrentDescription('does_it_include')"
              :rich-tips="getCurrentRichTips('does_it_include')"
            >
              <tpl_package_levy
                v-model="form.does_it_include"
                :disabled="getDisabled(orderKey)"
              ></tpl_package_levy>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <!-- 敏感信息 -->
        <template v-else-if="displayFactory('sensitive_info', orderKey)">
          <a-form-model-item
            prop="sensitive_info.0"
            :rules="getRulesFactory('sensitive_info')"
            :colon="false"
            :label="$t('21838')"
          >
            <DescMarkdownContent
              class="strong-tips"
              placement="right"
              :is-inline="false"
              :desc="getCurrentStrongTips('sensitive_info')"
              :line-clamp="3"
            />

            <ShimAntdTooltip
              placement="right"
              overlay-class-name="common-tooltip-style"
              :title="getCurrentDescription('sensitive_info')"
              :rich-tips="getCurrentRichTips('sensitive_info')"
            >
              <a-checkbox-group
                :disabled="getDisabled(orderKey)"
                class="commom-checkbox-group"
                v-model="form.sensitive_info"
              >
                <a-checkbox
                  v-for="item in sensitive_info_list"
                  :key="item.key"
                  :value="item.key"
                  :disabled="item.disabled"
                >
                  {{ item.value }}
                </a-checkbox>
              </a-checkbox-group>
            </ShimAntdTooltip>
          </a-form-model-item>
        </template>

        <EsimTopUpInfo
          v-else-if="displayFactory('esim_topup_info', orderKey)"
          :form="form"
          :disabled="getDisabled(orderKey)"
          :description="getCurrentStrongTips(orderKey)"
          :tooltip="getCurrentStrongTips(orderKey)"
          :required="getRequiredByKey(orderKey)"
        >
        </EsimTopUpInfo>

        <a-form-model-item
          v-else-if="displayFactory('sg_culture_pass_id', orderKey)"
          prop="sg_culture_pass_id"
          :rules="getRulesFactory('sg_culture_pass_id')"
          :colon="false"
          :label="$t('SG Culture Pass ID')"
        >
          <DescMarkdownContent
            class="strong-tips"
            placement="right"
            :is-inline="false"
            :desc="getCurrentStrongTips('sg_culture_pass_id')"
            :line-clamp="3"
          />

          <ShimAntdTooltip
            :title="getCurrentDescription('sg_culture_pass_id')"
            :rich-tips="getCurrentRichTips('sg_culture_pass_id')"
          >
            <a-input
              v-model="form.sg_culture_pass_id"
              :disabled="getDisabled(orderKey)"
              :placeholder="$t('global_please_input')"
            ></a-input>
          </ShimAntdTooltip>
        </a-form-model-item>
      </div>
    </a-form-model>
  </div>
</template>

<script>
import tpl_special_multi_language_input from '../tpl_special_multi_language_input'
import tpl_package_min_max_booking from '../tpl_package_min_max_booking'
import StandalonePkgs from '../comboComponents/standalonePkgs'
import QuestionIcon from '@activity/pages/package/units/components/QuestionIcon.vue'
import AutoPubUnpub from './components/AutoPubUnpub.vue'
import { checkPassAsync } from '@activity/components/pass-standard-confirm'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'
import StrongTips from '@activity/components/StrongTips.vue'
import NumberUnitSelector from '@activity/components/number-unit-selector/index.vue'
import WeekTimeslot from '@activity/components/week-timeslot/index.vue'
import EsimTopUpInfo from './components/esimTopUpInfo.vue'

import {
  FNB_TICKET_TYPE,
  fnb_package_types,
  confirmation_time_options,
  VOUCHER_RETRIEVAL_METHOD,
  voucher_levels,
  cancel_policy,
  voucher_usages,
  dynamic_voucher_code_enum,
  dynamic_voucher_code_options,
  ordered_voucher_usages_keys,
  voucher_get_methods,
  MERCHANT_PKG_CONTACT_TYPE,
  _extendMonthOptions,
  _groupDayOptions,
  instantConfirmationTimeOption,
  newConfirmationTimeOptions,
  OPEN_TICKET_WITHOUT_CALENDAR,
  RESERVATION_CONTACT_TYPE,
  weekOptions,
  StockOutValDict
} from '@activity/pages/package/package_const.js'

import MerchantSelect from '../tpl_package_merchant/merchant_select'
import tpl_package_levy from '../tpl_package_levy'
import { getCurrentLocal, getCalculatedDate } from '@activity/utils'
import { mapMutations, mapActions, mapState } from 'vuex'
import { getEditLang, getRefLang, pmsConfirm } from '@activity/utils'

import MultiStructDatePicker from '../widgets/Attribute/components/MultiStructDatePicker'
import OptionBindingTitles from '@activity/pages/package/basicInfo/components/optionBindingGroup'
import FieldBindingGroup from '@activity/pages/package/basicInfo/components/fieldBindingGroup'
import { reservationProductsMixin } from '@activity/pages/package/mixins/index'
import groupSummary from '@activity/components/groupSummary'
import imageUpload from '@activity/components/image-upload'
import ShimAntdTooltip from '../shimAntdTooltip'
import EscrowGuarantee from '../escrowGuarantee'

const FND_SUB_CATREGORY_ID = 4

// 加急项目，所以是在原有的 attribute 组件上，通过添加 mixin 的方式做特殊处理来满足需求
// 后期迭代需重写基本内容属性组件的时候，删掉 mixin 就是原有的运行逻辑了
const basic_info_attribute_mixin = {
  components: {
    MultiStructDatePicker,
    groupSummary,
    StrongTips,
    imageUpload
  },
  provide() {
    // `provide` can be hoisted up in higher component
    return {
      activity_id: +this.activity_id,
      ref_lang: getRefLang(),
      edit_lang: getEditLang(),
      locale_info: this.locale_info
    }
  },
  data() {
    this.fieldAttrGroupNameDict = {
      validity_model: __('200772'),
      activation_validity: __('42687'),
      usage_validity: __('42688'),
      aid_voucher_type_desc: __('Aid voucher type desc'),
      participation_validity: 'Participation Validity'
    }

    this.StockOutValDict = StockOutValDict

    return {
      locale_info: {
        react_data: []
      },
      all_variables: [],

      renderComponentOnRefTag: {
        non_available_date_options_a: (h, ctx) => {
          let params = {
            props: ctx.data.attrs,
            on: ctx.listeners
          }

          return <MultiStructDatePicker {...params}></MultiStructDatePicker>
        }
      },

      packageImageCloudinaryOptions: {
        cloud_name: 'klook',
        multiple: false,
        upload_preset: 'k8xu3bkg',
        sources: ['local']
      },

      allGroupsSummary: [],
      // ticket type 默认收起 group
      validityChunkIsUnfold: false
    }
  },
  computed: {
    ...mapState(['isMerchant']),
    hasTicketTypeField() {
      return this.orderList.includes('ticket_type')
    },
    disabledConfirmationTime() {
      return this.isMerchant && this.form.inventory_type === StockOutValDict.API
    },
    showFieldAttrGroupSummary() {
      return this.allGroupsSummary.some((curr) => {
        return curr.groupsSummary.some((group) => group.summary.length)
      })
    },
    ticketTypeOpts() {
      if (this.isPassProducts) {
        return {
          fnb_open_ticket: 0
        }
      }

      return this.fnb_ticket_types
    },
    validityModelOpts() {
      return [
        {
          label: __('42685'),
          value: 0,
          disabled: this.isPassProducts
        },
        {
          label: __('42686'),
          value: 1,
          disabled: this.form.ticket_type === 1
        },
        {
          label: 'Participation validity and usage validity',
          value: 2,
          disabled: !this.isPassProducts
        }
      ]
    },
    displayValidityModel() {
      return !(this.form.ticket_type === 1 && !this.isPassProducts)
    },
    getCalculatedDateTips() {
      const { calendar_extend } = this.form
      const type = calendar_extend.type === 0 ? 'days' : 'months'
      const diff = calendar_extend.type === 0 ? calendar_extend.days : calendar_extend.months
      return this.$t('83350', getCalculatedDate(diff, type))
    }
  },
  async mounted() {
    this.locale_info.react_data = await ajax.get(ADMIN_API.act.get_activity_status_by_langs, {
      params: {
        activity_id: this.activity_id,
        language_list: getEditLang()
      }
    })

    this.$nextTick(() => {
      if (this.isPassProducts && this.form.ticket_type === 1) {
        this.form.ticket_type = ''

        if (this.form.validity_model === 0) {
          this.form.validity_model = undefined
        }
      }
    })
  },
  async created() {
    this.all_variables = await this.getActVariables2actions()
  },
  methods: {
    ...mapMutations({
      setInvalidTagOnField: 'attr/setInvalidTagOnField'
    }),
    ...mapActions(['getActVariables2actions']),
    getTicketTypeDisabled(value) {
      const { isAttReservation } = this
      const { confirmationTime, reservation_required } = this.form
      if (this.isOpenDate(value) && this.isNewConfirmationTimeOptions(confirmationTime)) {
        return true
      }
      // spa 预约时fixed day不能选，att的预约可以
      if (reservation_required === 1 && value === 1 && !isAttReservation) {
        return true
      }
      return false
    },
    getOpenDateTicketTypeDisabled() {
      const confirmationTime = this.form.confirmation_time
      if (this.isNewConfirmationTimeOptions(confirmationTime) || this.supportPresale) {
        return true
      }

      const { disabledOpenDateType, disabledOpenTicket } = this
      return disabledOpenDateType || disabledOpenTicket
    },
    isOpenDate(value) {
      return [0, 3].includes(value)
    },
    isNewConfirmationTimeOptions(value) {
      return newConfirmationTimeOptions.includes(value)
    },
    getConfig(schema) {
      return schema?.config ?? {}
    },
    validityChunkSwitchFold(unfoldAll = true) {
      const fieldBindingGroup = [
        'participation_validity',
        'activation_validity',
        'usage_validity',
        'aid_voucher_type_desc'
      ]
      fieldBindingGroup.forEach((key) => {
        let content = this.$refs[key]?.[0]?.$refs?.content

        if (unfoldAll) {
          content?.unfoldAllGroups?.()
        } else {
          content?.foldAllGroups?.()
        }
        content = null
      })
    },
    displayOptionBindingTitlesComp(orderKey) {
      const display = this.displayFactory(orderKey, orderKey)
      const fieldKey = Object.keys(this.getOptionBindingTitlesCompConf())

      return fieldKey.includes(orderKey) && display
    },
    getOptionBindingTitlesCompConf(orderKey) {
      const dict = {
        cancellation_policy: {
          attr: {
            disabledChosen: this.getDisabled(orderKey) || this.disabledCancellation,
            calcOptDisabledFunc: this.calcDisabledCancellationPolicyOpt,
            formItemLabel: this.$t('cancellation_policy'),
            defaultOptions: this.getOptions2Obj(cancel_policy),
            refForm: { cancellation_policy: this.refOptionGroup }
          }
        },
        confirmation_time: {
          attr: {
            disabledChosen: this.getDisabled(orderKey) || this.disabledConfirmationTime,
            calcOptDisabledFunc: this.calcDisabledConfirmationTimeOpt,
            formItemLabel: this.$t('confirmation_time'),
            defaultOptions: confirmation_time_options,
            refForm: { confirmation_time: this.refOptionGroup }
          },
          listeners: {
            change: this.restrictChange
          }
        },
        voucher_get_method: {
          attr: {
            disabledChosen: this.getDisabled(orderKey),
            formItemLabel: this.$t('ob_voucher_retr_method'),
            defaultOptions: this.getOptions2Obj(voucher_get_methods),
            type: 'radio',
            refForm: { voucher_get_method: this.refOptionGroup }
          }
        }
      }

      return orderKey ? dict[orderKey] : dict
    },
    getOptions2Obj(data) {
      return Object.entries(data).map((item) => {
        const [textId, value] = item

        return {
          label: __(textId),
          value
        }
      })
    },
    calcDisabledCancellationPolicyOpt({ option }) {
      const { e_voucher, cancel_policy, openDate, isComboPKG, comboCancellationPolicy, isPassNoVoucher } =
        this
      const value = +option.value
      const openDateState = openDate && value === cancel_policy.cancel_before_start
      const comboState = isComboPKG && ![cancel_policy.no_cancel, comboCancellationPolicy].includes(value)

      let cancelPolicyState = false
      if (isPassNoVoucher) {
        cancelPolicyState = ![
          cancel_policy.cancel_before_redeem,
          cancel_policy.cancel_with_conditions
        ].includes(value)
      } else {
        cancelPolicyState = !e_voucher && value === cancel_policy.cancel_before_redeem
      }

      //confirmation_time为非Instant confirmation的 Open ticket without calendar ，只能选择 no cancellation 和 cancel_before_redeem
      const shouldDisableForOpenDateNonInstant =
        this.openDate &&
        this.form.confirmation_time !== 1 &&
        value !== cancel_policy.no_cancel &&
        value !== cancel_policy.cancel_before_redeem
      return cancelPolicyState || openDateState || comboState || shouldDisableForOpenDateNonInstant
    },
    calcDisabledConfirmationTimeOpt({ option }) {
      const confirmationTime = option.value
      if (this.isOpenDate(this.form.ticket_type) && this.isNewConfirmationTimeOptions(confirmationTime)) {
        return true
      }

      // presale 的时候，非 instant confirm 的时间不能选
      if (this.supportPresale && confirmationTime !== instantConfirmationTimeOption) {
        return true
      }

      return confirmationTime < this.comboConfirmationTime
    },
    getFieldAttrGroupDataByField(field) {
      const data = this.schemaConfig[field]?.value ?? {}
      const groupList = data?.group_data?.groups_summary ?? []

      return {
        data,
        groupList
      }
    },
    changeGroupSummary({ field, groupsSummary }) {
      const index = this.allGroupsSummary.findIndex((item) => item.field === field)
      const data = {
        field,
        groupsSummary
      }

      if (-1 !== index) {
        this.allGroupsSummary.splice(index, 1, data)
      } else {
        let allGroupsSummary = _.cloneDeep(this.allGroupsSummary)
        allGroupsSummary.push(data)
        // 根据 orderList 的顺序去做 group summary 的展示
        this.$set(
          this,
          'allGroupsSummary',
          allGroupsSummary.sort((a, b) => {
            return _.indexOf(this.orderList, a.field) - _.indexOf(this.orderList, b.field)
          })
        )
        allGroupsSummary = null
      }

      this.initFloorTimelineData()
    },
    onChangeData() {
      this.$emit('changeData')
    },
    resetGroupDataAndSummary(list = []) {
      list.forEach((field) => {
        let instance = _.get(this.$refs, `${field}.0`, null)
        // 初始化成功之后，若修改 ticket_type 的值需要重置 group 的数据
        if (instance) {
          instance.initGroupData()
          instance.getGroupsSummary()
          instance = null
        }
      })
    }
  }
}

export default {
  name: 'CommonField',
  components: {
    ShimAntdTooltip,
    FieldBindingGroup,
    OptionBindingTitles,
    tpl_package_levy,
    tpl_special_multi_language_input,
    MerchantSelect,
    tpl_package_min_max_booking,
    DescMarkdownContent,
    StandalonePkgs,
    QuestionIcon,
    EscrowGuarantee,
    AutoPubUnpub,
    NumberUnitSelector,
    WeekTimeslot,
    EsimTopUpInfo
  },
  directives: {
    linkDetail: {
      // this is deprecated after structed act

      // this could be put inside `change` event, use vue directive is to seperate concern
      update(el, binding, vnode) {
        /*
         * The directive’s value may or may not have changed,
         * but you can skip unnecessary updates by comparing the binding’s current and old values (see below on hook arguments).
         * https://vuejs.org/v2/guide/custom-directive.html#Hook-Functions
         */
        if (binding.value != binding.oldValue) {
          // https://stackoverflow.com/questions/43081391/vue-directive-to-access-the-vue-instance
          vnode.context.$root.$emit('link-tag', [binding.arg, binding.value])
        }
      }
    }
  },
  mixins: [basic_info_attribute_mixin, reservationProductsMixin],
  install(Vue) {
    // https://vuejs.org/v2/guide/plugins.html
    Vue.component(this.name, this)
  },
  model: {
    prop: 'form',
    event: 'change'
  },
  props: {
    hideDescTips: {
      type: Boolean,
      default: false
    },
    hideStrongTips: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    keyFlag: {
      type: String,
      default: ''
    },
    orderList: {
      type: Array,
      required: true
    },
    form: {
      type: Object,
      required: true
    },
    pkg: {
      type: Object,
      required: true
    },
    schemaConfig: {
      type: Object,
      required: true
    },
    lockAutoPublish: {
      type: Boolean,
      default: false
    },
    lockAutoWarmUp: {
      type: Boolean,
      default: false
    },
    disabledOpenTicket: {
      type: Boolean,
      default: true
    },
    refForm: {
      type: Object,
      default: () => ({})
    },
    refOptionGroup: {
      type: Object,
      default: () => ({})
    },
    comboConfirmationTime: {
      type: Number,
      default: 1
    },
    comboCancellationPolicy: {
      type: Number,
      default: 1
    },
    optionGroup: {
      type: Object,
      default: () => ({})
    },
    originalData: {
      type: Object,
      required: true
    },
    isPublishWithAi: {
      type: Boolean,
      default: false
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    supportPresale: {
      type: Boolean,
      default: false
    },
    pkgBasicInfoFieldTips: {
      type: Array,
      default: () => []
    },
    // aid
    customTicketTypes: {
      type: Object,
      default: null
    },
    hideOpenTicketWithoutCalendar: {
      type: Boolean,
      default: false
    },
    customStockOutOptions: {
      type: Array,
      default: null
    }
  },
  inject: ['reloadPage2provide', 'refreshPage', 'initFloorTimelineData', 'calcAttrGroupDisplayState'],
  data() {
    return {
      merchantObj: {},
      fnb_package_types,
      data_inited: false,
      cancel_policy,
      voucher_levels,
      voucher_usages,
      dynamic_voucher_code_enum,
      dynamic_voucher_code_options,
      ordered_voucher_usages_keys,
      voucher_get_methods,
      offline_voucher: [3, 4],

      VOUCHER_RETRIEVAL_METHOD,
      activity_id: +(this.$route.query.activity_id || this.$route.params.id),

      platforms: {
        0: this.$t('act_lan_account_both'),
        1: this.$t('act_lan_user_only'),
        2: this.$t('act_lan_agent_only')
      },
      pre_inventory_type: '',
      confirmation_time_options,
      docLink:
        'https://docs.google.com/presentation/d/1Y0gmyiIoktzLu6DwmKVIq5WFysrWuP04P_cl4-QLYUs/edit#slide=id.gdb2fa2f38e_0_231',
      iconImages: [
        {
          url: 'https://res.klook.com/image/upload/v1656657997/web3.0/********-144500.jpg'
        },
        {
          url: 'https://res.klook.com/image/upload/v1656657986/web3.0/********-144446.jpg'
        }
      ],
      showminGroupVisible: false,
      minGroupChangeTip: '',
      cacheGuardGroupoldMinValue: 0,
      formatOptions: [],
      // 当 admin 改成 pic 选项的话，pic 还是会展示在MP
      openSelectionPicOnMer: false,

      // 有些属性要组合成一个分组，通过这个配置组织
      fieldGroups: {
        available_amendment_group: [
          'reservation_week_timeslot',
          'reservation_block_out_date',
          'reservation_cut_off_time'
        ]
      }
    }
  },
  computed: {
    fnb_ticket_types() {
      return this.customTicketTypes || FNB_TICKET_TYPE
    },
    checkAddDisabled() {
      const reservation_week_timeslot = this.form.reservation_week_timeslot
      if (!Array.isArray(reservation_week_timeslot)) {
        return false
      }
      const list = reservation_week_timeslot.reduce((acc, curr) => {
        const { week_day = [] } = curr
        return [...acc, ...week_day]
      }, [])
      return list.length >= weekOptions.length
    },
    hasReservationPart() {
      return _.has(this.form, 'reservation_required')
    },
    isAttReservation() {
      return this.hasReservationPart && _.has(this.form, 'reservation_others_cut_off_time')
    },
    reservatioCutOffTimeText() {
      return this.getTextList(this.$t('121724'))
    },
    reservationOthersCutOffTimeText() {
      return this.getSplitTextList(this.$t('174482'))
    },
    reservationAmendmentPolicyText() {
      return this.getTextList(this.$t('121714'))
    },
    hideReservationFileds() {
      const reservation_required = this.form.reservation_required
      return reservation_required && reservation_required != 2
    },
    hideReservationMethodAfterFileds() {
      const hideReservationFileds = this.hideReservationFileds
      return hideReservationFileds && this.form.reservation_method === 1
    },
    subCategoryId() {
      return this.$store.state.categoryInfo?.sub_category_id
    },
    showReservationMethodOptions() {
      // const subCategoryId = this.subCategoryId
      // const spaCategoryId = global_env.isProd ? [413] : [421]
      // const isSpa = spaCategoryId.includes(subCategoryId)
      // 增加att业务
      // return isSpa || this.isAttReservation
      return true
    },
    isFnd() {
      return +this.subCategoryId === FND_SUB_CATREGORY_ID
    },
    calcMerchantDisabled() {
      return function (orderKey) {
        return 'confirm_order' in this.merchantObj
          ? this.merchantObj.confirm_order === 0
          : this.getDisabled(orderKey)
      }
    },
    sensitive_info_list() {
      let reqData = this.originalData
      let si = this.form.sensitive_info
      let arr = (reqData && _.merge([], reqData.sensitive_info_list)) || []
      let leng = arr.length - 1
      if (si.length === 0) {
        arr.forEach((item) => this.$set(item, 'disabled', false))
      } else if (si[0] === arr?.[leng]?.key) {
        arr.forEach((item, i) => this.$set(item, 'disabled', i !== leng))
      } else {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        arr[leng] !== undefined && this.$set(arr[leng], 'disabled', true)
      }
      return arr
    },
    merchantPkgContactType() {
      return MERCHANT_PKG_CONTACT_TYPE
    },
    reservationContactType() {
      return RESERVATION_CONTACT_TYPE
    },
    _extendDayOptions() {
      const cruiseSubCategoryIds = global_env.isProd ? [558] : [600]
      const days = cruiseSubCategoryIds.includes(this.subCategoryId) ? 365 * 2 : 365

      return Array(days)
        .fill()
        .map((item, index) => ({
          value: index + 1,
          label: index + 1
        }))
    },
    _extendMonthOptions() {
      return _extendMonthOptions
    },
    _groupDayOptions() {
      return _groupDayOptions
    },
    package_id() {
      return +this.pkg.package_id
    },
    e_voucher() {
      return this.form.voucher_get_method === this.voucher_get_methods.e_voucher
    },
    disableVoucherLevel() {
      // 保险活动也disable， 目前只在后端判断
      return this.form.inventory_type === StockOutValDict.API
    },
    showVoucherType() {
      return this.form.inventory_type === StockOutValDict.OTHERS
    },
    merchant_confirm_types() {
      return [
        {
          name: this.$t('klk_voucher_klk_code'),
          id: 1
        },
        ...klook.mergeIf(this.form.confirmation_time !== 1, [
          {
            name: this.$t('klk_voucher_merchant_code'),
            id: 2
          },

          {
            name: this.$t('merchant_voucher'),
            id: 3
          }
        ])
      ]
    },
    stockOutOptions() {
      const allOpts = this.customStockOutOptions || [
        {
          label: this.isMerchant ? this.$t('172673') : 'API',
          desc: this.isMerchant ? this.$t('172674') : '',
          // tips: this.isMerchant // eslint-disable-next-line prettier/prettier, quotes
          //   ? this.$t('172676')
          //   : '',
          value: StockOutValDict.API
        },
        { label: 'INVENTORY', value: StockOutValDict.INVENTORY },
        {
          label: this.isMerchant ? this.$t('172675') : 'Others',
          value: StockOutValDict.OTHERS
        }
      ]

      const baseOpts = allOpts.filter((item) => item.value !== 'INVENTORY')

      if (this.isMerchant) {
        if (this.openSelectionPicOnMer && this.form.confirmation_time === 1) {
          return allOpts
        }

        return baseOpts
      }

      return this.form.confirmation_time === 1 ? allOpts : baseOpts
    },

    // open date tiket
    openDate() {
      return this.form.ticket_type === 3
    },
    disabledCancellation() {
      if (this.isPassProducts) {
        return false
      }
      return false
      // return this.openDate && this.form.confirmation_time !== 1
    },
    disabledCalendarMonth() {
      return this.openDate
    },
    disabledOpenDateType() {
      // 1. 如果取消政策是 cancel_before_start,则禁用
      if (this.form.cancellation_policy === this.cancel_policy.cancel_before_start) {
        return true
      }
      const { inventory_type } = this.form
      const isInventoryType = inventory_type === StockOutValDict.INVENTORY
      // 2.后端下发inventory_open_ticket_without_calendar代表白名单活动，可以进行open_ticket_without_calendar
      if (isInventoryType && this.originalData?.inventory_open_ticket_without_calendar && this.isCreate) {
        return false
      }
      // 3. 原有逻辑,只允许 OTHERS 或 API 类型
      return ![StockOutValDict.OTHERS, StockOutValDict.API].includes(inventory_type)
    },
    usageValidityFieldRequired() {
      const { ticket_type, inventory_type } = this.form
      return ticket_type === OPEN_TICKET_WITHOUT_CALENDAR && inventory_type === StockOutValDict.API
    },
    isComboPKG() {
      return this.form.product_type === 1
    },
    isPassNoVoucher() {
      return this.isPassProducts && this.form.voucher_get_method === this.voucher_get_methods.no_voucher
    }
  },
  watch: {
    showReservationMethodOptions: {
      immediate: true,
      handler(v) {
        if (!v) {
          this.form.reservation_method = 2
        }
      }
    },
    'form.reservation_method': {
      immediate: true,
      handler(value) {
        if (value === 1) {
          this.form.show_account_type = 1
        }
      }
    },
    'form.reservation_required': {
      immediate: true,
      handler(value) {
        const { isAttReservation } = this
        // att的预约不做此限制
        if (value === 2 && !isAttReservation) {
          this.form.reservation_method = undefined
        }
      }
    },
    'form.cancellation_policy': {
      immediate: true,
      handler(value) {
        if (value === 0) {
          this.form.reservation_no_show_policy = 1
        }
      }
    },
    'form.product_type'(productType) {
      // 如果是combo，则inventory_type直接为other
      if (productType === 1) {
        this.form.inventory_type = StockOutValDict.OTHERS
      }
    },
    'form.validity_model'() {
      this.validityChunkIsUnfold = true
    },
    'form.ticket_type'(v) {
      if (this.openDate && this.form.confirmation_time !== 1) {
        this.form.cancellation_policy = 9
      }

      if (v === OPEN_TICKET_WITHOUT_CALENDAR) {
        if (this.form.inventory_type === StockOutValDict.INVENTORY) {
          if (this.isCreate && this.originalData?.inventory_open_ticket_without_calendar) {
            return
          }
          this.form.inventory_type = undefined
        }
      }
      if (this.isOpenDate(v) && this.isNewConfirmationTimeOptions(this.form.confirmation_time)) {
        this.form.ticket_type = undefined
      }

      if (v !== 1 && hasOwnProperty.call(this.form, 'guarantee_group')) {
        Object.keys(this.form.guarantee_group).forEach((key) => {
          this.form.guarantee_group[key] = 0
        })
      }

      this.validityChunkIsUnfold = true
    },
    'form.package_type': {
      immediate: false,
      handler(v) {
        if (this.isCreate) {
          if ([6, 7, 8].includes(v)) {
            this.form.ticket_type = this.fnb_ticket_types.fnb_open_ticket
          } else if ([9, 10, 11].includes(v)) {
            this.form.ticket_type = this.fnb_ticket_types.fnb_fixed_date
          }
        }
      }
    },
    'form.pkg_merchant': {
      handler(v, oldV) {
        if (v && oldV) {
          this.$nextTick(() => {
            this.form.merchant_confirm = 0
          })
        }
        // combo 时强制更新merchant
        if (v && this.form.product_type === 1) {
          klook.bus.$emit('updateMerchants', v)
        }
      }
    },
    validityChunkIsUnfold: {
      immediate: true,
      handler(unfoldAll) {
        this.$nextTick(() => {
          !this.isCreate && this.validityChunkSwitchFold(unfoldAll)
        })
      }
    },
    // handle default value
    'form.confirmation_time'(v) {
      if (v == 0) {
        this.form.confirmation_time = undefined
      }

      if (this.isNewConfirmationTimeOptions(v) && this.isOpenDate(this.form.ticket_type)) {
        this.form.ticket_type = undefined
      }

      // open date
      const confirmation_time = this.form.confirmation_time
      if (this.openDate && confirmation_time && confirmation_time !== 1) {
        this.form.cancellation_policy = 9
      }

      let types = this.merchant_confirm_types
      if (types.every((item) => item.id !== this.form.voucher_type)) {
        this.form.voucher_type = types.length === 1 ? types[0].id : 0
      }
    },
    'form.voucher_get_method'(v) {
      if (this.isPassNoVoucher) {
        this.form.cancellation_policy = undefined

        return
      }
      if (v !== this.voucher_get_methods.e_voucher) {
        if (this.form.cancellation_policy === this.cancel_policy.cancel_before_redeem) {
          this.form.cancellation_policy = undefined
        }
        this.form.voucher_usage = 0
        this.form.voucher_code_level = undefined
      }
    },
    'form.inventory_type': {
      immediate: true,
      handler(val) {
        if (val === StockOutValDict.INVENTORY) {
          this.openSelectionPicOnMer = true
        } else if (this.isMerchant && val === StockOutValDict.API) {
          // 1: Instant confirmation
          this.form.confirmation_time = 1
        }
      }
    }
  },
  mounted() {
    this.data_inited = true
    // 保存上一次的库存类型
    this.pre_inventory_type = this.form.inventory_type
    if (this.form?.guarantee_group) {
      const min_group_value = this.form.guarantee_group.min_group || 0
      this.formatOptions = _groupDayOptions.filter((item) => item.value > min_group_value)
      this.$watch('form.guarantee_group.min_group', this.handleMinGroupChange, { deep: true })
    }
  },
  deactivated() {
    this.isDeactivated = true
  },
  methods: {
    checkHttp({ content, type }) {
      if (type === 3) {
        return !/^(http|https):\/\/([\w.]+\/?)\S*/.test(content)
      }
      return false
    },
    getOtherTimeslotWeek(index) {
      const { reservation_week_timeslot = [] } = this.form
      return reservation_week_timeslot.reduce((acc, curr, idx) => {
        const { week_day = [] } = curr
        if (index === idx) {
          return acc
        } else {
          return [...acc, ...week_day]
        }
      }, [])
    },
    getTextList(text = '') {
      const str = text.replace(/\{num\}[\s\S]*\{hours_days\}/, '{component_area}')
      return str.split('{component_area}')
    },
    getSplitTextList(text = '') {
      const str = text.replace(/{num}\s*\{\w+\}/, '{component_area}')
      return str.split('{component_area}')
    },
    showAccountTypeOptionsDisabled(v) {
      return [0, 2].includes(v) && this.form.reservation_method === 1
    },
    handleAddReservationBlockOutDate() {
      this.form.reservation_block_out_date.push([null, null])
    },
    handleDeleteReservationBlockOutDate(index) {
      this.form.reservation_block_out_date.splice(index, 1)
    },
    handleReservationBlockOutDateChange(value, index) {
      this.form.reservation_block_out_date.splice(index, 1, value)
    },
    handleReservationContactAdd() {
      this.form.reservation_other_content.contact.push({ content: '', type: undefined })
    },
    handleReservationContactDel(index) {
      this.form.reservation_other_content.contact.splice(index, 1)
    },
    handleReservationWeekTimeslotAdd() {
      if (this.checkAddDisabled) {
        return
      }
      this.form.reservation_week_timeslot.push({
        week_day: [],
        timeslot: [{ start: undefined, end: undefined }]
      })
    },
    handleTimeslotDelete(index) {
      this.form.reservation_week_timeslot.splice(index, 1)
    },
    handleChangeReservationCutTimeType(e) {
      const type = e?.target?.value
      if (type === 1) {
        this.form.reservation_others_cut_off_time.time_config = {
          time_num: undefined,
          time_unit: 'hours'
        }
      }
    },
    handleReservationWeekTimeslotChange(value, index) {
      this.form.reservation_week_timeslot.splice(index, 1, value)
    },
    changeAutoSuspend(value) {
      this.form.auto_suspend_sale = value ? 1 : 0
    },
    getContactStyle(mpc) {
      const media = mpc?.media ?? ''
      const isCustom = media == 'customized'
      if (isCustom) {
        return {
          width: '135px'
        }
      }
      return {
        width: '280px'
      }
    },
    onContactChange(mpc) {
      if (mpc.media === 'phone' && mpc.number?.length) {
        const newNumber = mpc.number.replace(/ /g, '')
        if (newNumber.length !== mpc.number) {
          mpc.number = newNumber
        }
      }
    },
    getRequiredByKey(orderKey) {
      return !!this.schemaConfig[orderKey]?.required ?? false
    },
    getSuggestFillingByKey(orderKey) {
      return !!this.schemaConfig[orderKey]?.config?.suggest_filling ?? false
    },
    getInventoryTypeDisableStatus(currValue) {
      if (currValue !== 'INVENTORY') {
        return false
      }
      const { ticket_type } = this.form
      return !!(+ticket_type === OPEN_TICKET_WITHOUT_CALENDAR)
    },
    handleMinGroupChange(newVal, oldVal) {
      oldVal = oldVal || 0
      newVal = newVal || 0

      if (oldVal < newVal) {
        this.minGroupChangeTip = this.$t('83610', { num1: oldVal, num2: newVal })
        this.cacheGuardGroupoldMinValue = oldVal
        this.showminGroupVisible = true
      }

      this.formatOptions = _groupDayOptions.filter((item) => item.value > newVal)
    },
    handleGroupCancel() {
      this.form.guarantee_group.min_group = this.cacheGuardGroupoldMinValue
      this.formatOptions = _groupDayOptions.filter((item) => item.value > this.cacheGuardGroupoldMinValue)
    },
    changeVoucherUsage(value) {
      const { print_mobile_voucher, 80333: print_mobile_offline_voucher } = voucher_usages

      this.form.voucher_usage = value ? print_mobile_offline_voucher : print_mobile_voucher
    },
    previewImage(file) {
      let EscrowGuarantee = this.$refs.EscrowGuarantee || []
      if (EscrowGuarantee[0]) {
        EscrowGuarantee[0].handlePreview(file)
        EscrowGuarantee = null
      }
    },
    getBlankLink(text, link) {
      return text.replace('"MULTILANG_URL_PLACEHOLDER"', `"${link}" target="_blank"`)
    },
    changeMerchant(mid, midObj) {
      this.merchantObj = midObj
    },
    getDisabled(key) {
      let item = this.schemaConfig[key]
      if (!item) {
        return true
      }
      return item.access_permission === 1
    },
    // tpl_package_merchant start
    handleDelMerchantPkgContact(obj) {
      this.form.pkg_contact.splice(
        _.findIndex(this.form.pkg_contact, (item) => obj === item),
        1
      )
    },
    handleAddMerchantPkgContact() {
      this.form.pkg_contact.push({
        media: '',
        number: ''
      })
    },
    getMultiAttrFactory(type, conf = {}) {
      let current = this.schemaConfig[type] || {}

      return {
        onlyEditLanguage: current.onlyEditLanguage,
        required: current.required,
        requiredEn: current.requiredEn,
        valField: type,
        label: getCurrentLocal(this.schemaConfig, {
          type,
          field: 'name_multilang',
          defaultVal: this.$t('package_info_name')
        }),
        placeholder: this.$t('global_please_input'),
        description: getCurrentLocal(this.schemaConfig, {
          type,
          field: 'desc_multilang',
          defaultVal: ''
        }),
        richTips: this.pkgBasicInfoFieldTips.find((item) => item.field === type),
        strongTips: this.getCurrentStrongTips(type),
        ...conf
      }
    },
    // tpl_package_info end
    getCurrentDescription(type) {
      return this.hideDescTips ? '' : this.getCurrentLocal(type, 'desc_multilang', '')
    },
    getCurrentRichTips(type) {
      return this.schemaConfig[type]?.richTips ?? null
    },
    getCurrentStrongTips(type) {
      return this.hideStrongTips ? '' : this.getCurrentLocal(type, 'obvious_multilang', '')
    },
    getCurrentLocal(type, field = 'desc_multilang', defaultVal = 'Please select') {
      return getCurrentLocal(this.schemaConfig, {
        field,
        type,
        defaultVal
      })
    },
    getCurrentDescription2tplPackageMerchant(type) {
      return getCurrentLocal(this.schemaConfig || {}, {
        type,
        field: 'desc_multilang',
        defaultVal: ''
      })
    },
    getGroupRequired(key = '') {
      const list = _.get(this.fieldGroups, key, [])
      return list.some((item) => {
        return _.get(this.schemaConfig, `${item}.required`, false)
      })
    },
    displayGroupFactory(key = '', orderKey) {
      if (orderKey && orderKey !== key) {
        return false
      }

      const list = _.get(this.fieldGroups, key, [])

      if (!Array.isArray(list)) {
        return false
      }

      return list.some((item) => {
        return this.displayFactory(item)
      })
    },
    displayFactory(field, orderKey) {
      if (orderKey && orderKey !== field) {
        return false
      }
      let item = this.schemaConfig[field]
      return item ? item.access_permission > 0 : false
    },
    getRulesFactory(field) {
      return _.get(this.schemaConfig, `${field}.rules`, {})
    },
    async restrictChange(key = '') {
      if (
        !this.isDeactivated &&
        this.data_inited &&
        this.pkg.auto_pub_unpub &&
        this.pkg.auto_pub_unpub.package_published
      ) {
        const confirm = await this.confirmPromise({
          title: __('pkg_restrict_hint')
        })
        if (confirm.confirm) {
          this.unpublishPkg()
          return
        } else if (confirm.cancel) {
          this.reloadPage2provide()
          return
        }
      }

      const inventory_type = this.form.inventory_type
      const pre_inventory_type = this.pre_inventory_type
      const isChangeInventory = key === 'inventory_type'
      // INVENTORY 和其他类型切换标记
      const changeType =
        (inventory_type === StockOutValDict.INVENTORY || pre_inventory_type === StockOutValDict.INVENTORY) &&
        pre_inventory_type &&
        isChangeInventory
      const message = pre_inventory_type === StockOutValDict.INVENTORY ? this.$t('48295') : this.$t('48296')

      if (changeType) {
        const picConfirm = await this.confirmPromise({
          title: message,
          okText: this.$t('48297')
        })
        if (picConfirm.confirm) {
          this.changeInverntory(inventory_type, pre_inventory_type)
        } else {
          this.form.inventory_type = pre_inventory_type
        }
        return
      }
      if (isChangeInventory) {
        this.changeInverntory(inventory_type, pre_inventory_type)
      }
    },
    async changeInverntory(inventory_type, pre_inventory_type) {
      const check_itinerary_map = await this.checkItineraryMap(inventory_type, pre_inventory_type)
      const invType = check_itinerary_map ? inventory_type : pre_inventory_type
      this.form.inventory_type = invType
      this.pre_inventory_type = invType
    },
    async checkItineraryMap(cv, ov) {
      if (cv === StockOutValDict.API && ov) {
        const resp = await ajax.getBody(ADMIN_API.act.check_itinerary_map, {
          params: {
            pkg_id: this.package_id
          }
        })
        if (resp && resp.success) {
          const result = resp.result || {}
          const need_user_confirm = result.need_user_confirm
          if (need_user_confirm) {
            const cofirm = await pmsConfirm.call(this, {
              content: result.note
            })
            return cofirm
          }
          return true
        }
        return false
      } else {
        return true
      }
    },
    confirmPromise(confirmOpt = {}) {
      return new Promise((resolve) => {
        let opt = {
          ...confirmOpt,
          onOk() {
            resolve({
              confirm: true,
              cancel: false
            })
          },
          onCancel() {
            resolve({
              confirm: false,
              cancel: true
            })
          }
        }
        this.$confirm(opt)
      })
    },
    async unpublishPkg() {
      // 下架活动、套餐、sku的时候需要检查是否是 pass standard
      const checkPassRes = await checkPassAsync({ package_ids: this.pkg.package_id }, this.$i18n)
      if (checkPassRes && checkPassRes.stop) {
        return
      }

      let data = {
        reason: checkPassRes.reason,
        reason_code: checkPassRes.reasonCode,
        force: !!checkPassRes.force,
        package_id: this.pkg.package_id,
        status: 0,
        page_from: klook.getPlatformRoleKey()
      }
      let res = await ajax.postBody(
        {
          url: ADMIN_API.act.update_status2pkg,
          data
        },
        ajax.sucOptions
      )
      res && res.success && this.refreshPage()
    },

    async validate(noTip = false) {
      let nameFlag = true,
        subnameFlag = true,
        activation_validity_flag = await this.getFieldGroupValidate('activation_validity'),
        usage_validity_flag = await this.getFieldGroupValidate('usage_validity'),
        aid_voucher_type_desc_flag = await this.getFieldGroupValidate('aid_voucher_type_desc'),
        participation_validity_flag = await this.getFieldGroupValidate('participation_validity')

      if (this.displayFactory('pkg_name') && this.$refs.pkg_name) {
        let ref = this.$refs.pkg_name
        let resp = await Promise.all(ref.map(async (item) => await item.validateForm()))
        ref = null
        nameFlag = resp.every((item) => item) && nameFlag
      }
      if (this.displayFactory('pkg_subname') && this.$refs.pkg_subname) {
        let ref = this.$refs.pkg_subname
        let resp = await Promise.all(ref.map(async (item) => await item.validateForm()))
        ref = null
        subnameFlag = resp.every((item) => item) && subnameFlag
      }

      const optionBindingTitlesFlag = (
        await Promise.all((this.$refs.optionBindingTitles || []).map((item) => item.validateGroup()))
      ).every((item) => item)

      let valid = await new Promise((resolve) =>
        this.$refs.form.validate((valid) =>
          resolve(
            valid &&
              nameFlag &&
              subnameFlag &&
              usage_validity_flag &&
              aid_voucher_type_desc_flag &&
              activation_validity_flag &&
              participation_validity_flag &&
              optionBindingTitlesFlag
          )
        )
      )
      if (!valid && !noTip) {
        this.$message.warn('Please fill in the form')
      }

      return valid
    },

    async getFieldGroupValidate(key) {
      if (this.displayFactory(key) && this.$refs[key]?.[0]) {
        let vm = this.$refs[key][0]

        if (!this.calcAttrGroupDisplayState(key, this.form)) {
          vm = null
          return true
        }

        let flag = await vm.validateGroup()
        vm.isInvalidate = !flag
        vm = null

        return flag
      }

      return true
    },

    validateField(field) {
      this.$refs.form.validateField(field)
    }
  }
}
</script>

<style lang="scss">
$spacePixel: 12px;

.is-disabled-picker {
  cursor: not-allowed;
  pointer-events: none;
  .ant-input {
    background-color: #eef1f6;
    border-color: #d1dbe5;
    color: #bbb;
    cursor: not-allowed;
    pointer-events: none;
  }
}
li.heng {
  position: relative;
  margin-left: 10px;
  list-style: disc;
  div {
    padding-left: 16px;
  }
}

.field-attr-summaries {
  background-color: #fafafa;
  padding: $spacePixel;
  margin-top: $spacePixel;
}

.form-item-custom-label {
  display: inline-block;
  text-align: left;
  white-space: normal;
}

.pre-cntent {
  word-break: break-all;
  white-space: pre-line;
}

.question-icon-img {
  width: 100%;
  display: block;
  margin-top: 10px;
  cursor: pointer;
}

.common-basic-form-style .mini-select {
  width: 200px;
}

.merchant-container {
  .common-field-container .form-item {
    max-width: calc(100% - 280px);
  }
}

.common-field-container {
  .ant-alert {
    background-color: #fffbe6; /* 黄色背景 */
    border-color: #ffe58f; /* 边框颜色 */
  }
}
</style>

<style lang="scss" scoped>
.common-inline-flex-column {
  display: inline-flex;
  flex-direction: column;
  gap: 12px;

  /* ::v-deep .anticon {
    color: rgba(0, 0, 0, 0.85);
  } */

  &__desc {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
  }
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: space-between;

  ::v-deep .anticon {
    font-size: 16px;
  }
}

.ant-radio-wrapper.flex-content {
  display: flex;
  align-items: center;

  .reservation-other-cut-off-time {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.hide-label {
  margin-top: 16px;

  ::v-deep .ant-form-item-label {
    display: none;
  }
}

.reservation-note {
  display: block;
  margin-top: 24px;
}

.reservation-add {
  display: block;
}

.reservation-del {
  font-size: 20px;
  color: #f44622;
  margin-left: 16px;
  cursor: pointer;
}

.reservation-input-group-wrap {
  margin-bottom: 16px;

  .error-message {
    color: #f5222d;
    line-height: 24px;
  }

  .reservation-input-group {
    display: flex;
    align-items: center;
  }
}

.special-form-item {
  background: #fafafa;
  padding: 0 12px;
  margin-bottom: 8px !important;

  .form-item-label {
    font-size: 14px;
    font-weight: 600;
  }
}

.reservation-date-add-btn {
  display: inline-flex;
  align-items: center;
  background: #ffffff;
  border-radius: 2px;
  border: 1px solid #4a4a4a;
  padding: 0 8px;
  height: 24px;
  box-sizing: border-box;
  cursor: pointer;

  &.disabled {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
    border-color: #d9d9d9;
  }

  &-icon {
    font-size: 14px;
    margin-right: 8px;
  }
}

.reservation-range-picker-wrap {
  margin: 8px 0;

  .reservation-range-picker {
    width: 240px;

    ::v-deep .ant-input {
      width: 100%;
    }
  }
}

.reset-label {
  min-height: unset !important;
}

.num-unit-wrap {
  .num-unit-comp {
    display: inline-flex;
  }

  .num-unit-text-left {
    margin-right: 16px;
    display: inline;
  }

  .num-unit-text-right {
    margin-left: 16px;
    display: inline;
  }
}

.week-timeslot-wrap {
  display: flex;

  .timeslot-del {
    font-size: 20px;
    color: #f44622;
    cursor: pointer;
    margin-top: 13px;
  }
}

.block-del {
  font-size: 20px;
  color: #f44622;
  cursor: pointer;
  margin-top: 13px;
  margin-left: 16px;
}
</style>
